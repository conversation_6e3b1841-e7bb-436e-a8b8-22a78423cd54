# E-commerce Analytics SaaS Platform - Production Environment Configuration
# This file overrides .env for production deployment
# PRODUCTION ENVIRONMENT - DO NOT COMMIT WITH ACTUAL SECRETS

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
DENO_ENV=production
NODE_ENV=production
BUILD_TARGET=production
LOG_LEVEL=info

# =============================================================================
# DENO 2 SERVICE PORTS (Production Configuration)
# =============================================================================
# Fresh Frontend (Server-Side Rendering + Islands)
FRESH_PORT=8000
DASHBOARD_FRONTEND_PORT=8000

# Deno 2 Backend Services
DASHBOARD_BACKEND_PORT=3000
ANALYTICS_PORT=3002
INTEGRATION_PORT=3001
BILLING_PORT=3003
ADMIN_PORT=3005

# High-performance Go service
LINK_TRACKING_PORT=8080

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# IMPORTANT: These values are injected from Kubernetes secrets
JWT_SECRET=${JWT_SECRET}
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=ecommerce-analytics-saas
JWT_AUDIENCE=ecommerce-analytics-users

# Strict CORS for production
CORS_ORIGIN=${CORS_ORIGIN}
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400

# Security headers
ENABLE_SECURITY_HEADERS=true
CONTENT_SECURITY_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' wss: ws:;"

# Production rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_BURST=200

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL + TimescaleDB Production)
# =============================================================================
# Production RDS Configuration (injected from Kubernetes secrets)
DB_HOST=${DB_HOST}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}
DB_SSL=true

# TimescaleDB Configuration
TIMESCALEDB_ENABLED=true
TIMESCALEDB_COMPRESSION_ENABLED=true
TIMESCALEDB_RETENTION_POLICY=365d

# Production pool settings (optimized for high load)
DB_POOL_MIN=10
DB_POOL_MAX=50
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000
DB_CONNECTION_TIMEOUT=30000

# Database URL format for Deno services
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=require

# =============================================================================
# REDIS CONFIGURATION (ElastiCache Production)
# =============================================================================
REDIS_HOST=${REDIS_HOST}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_DB=0

# Production Redis settings
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_DELAY_ON_FAILURE=200
REDIS_MAX_RETRY_ATTEMPTS=5

# Redis URL format for Deno services
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# =============================================================================
# FRESH FRONTEND CONFIGURATION (Production Service URLs)
# =============================================================================
# Service URLs for Fresh API proxy routes (Kubernetes internal)
DASHBOARD_API_URL=http://dashboard-service:3000
ANALYTICS_API_URL=http://analytics-service:3002
INTEGRATION_API_URL=http://integration-service:3001
BILLING_API_URL=http://billing-service:3003
ADMIN_API_URL=http://admin-service:3005
LINK_TRACKING_API_URL=http://link-tracking-service:8080

# Real-time streaming configuration
SSE_RECONNECT_TIMEOUT=2000
SSE_MAX_RECONNECT_ATTEMPTS=5
SSE_HEARTBEAT_INTERVAL=30000

# =============================================================================
# SSL AND SECURITY
# =============================================================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Cookie security
COOKIE_SECURE=true
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=strict

# =============================================================================
# STRIPE BILLING CONFIGURATION (Production)
# =============================================================================
STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
STRIPE_API_VERSION=2023-10-16

# Production price IDs
STRIPE_BASIC_PRICE_ID=${STRIPE_BASIC_PRICE_ID}
STRIPE_PROFESSIONAL_PRICE_ID=${STRIPE_PROFESSIONAL_PRICE_ID}
STRIPE_ENTERPRISE_PRICE_ID=${STRIPE_ENTERPRISE_PRICE_ID}
STRIPE_STRATEGIC_PRICE_ID=${STRIPE_STRATEGIC_PRICE_ID}

# =============================================================================
# MONITORING AND OBSERVABILITY (Production)
# =============================================================================
# Error tracking
SENTRY_DSN=${SENTRY_DSN}
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_RELEASE=${SENTRY_RELEASE}

# Prometheus metrics
ENABLE_METRICS=true
METRICS_PORT=9090
METRICS_PATH=/metrics
METRICS_COLLECTION_INTERVAL=15

# Health checks
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_PATH=/health

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DISTRIBUTED_TRACING=true
TRACING_SAMPLE_RATE=0.1

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=90
BACKUP_S3_BUCKET=${BACKUP_S3_BUCKET}

# =============================================================================
# LOGGING
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_PATH=/app/logs
LOG_RETENTION_DAYS=90

# Production logging settings
ENABLE_REQUEST_LOGGING=true
ENABLE_QUERY_LOGGING=true
SLOW_QUERY_THRESHOLD_MS=500
VERBOSE_LOGGING=false

# =============================================================================
# AWS CONFIGURATION (Production)
# =============================================================================
AWS_REGION=${AWS_REGION}
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}

# S3 Configuration
S3_BUCKET=${S3_BUCKET}
S3_EXPORT_BUCKET=${S3_EXPORT_BUCKET}
S3_BACKUP_BUCKET=${BACKUP_S3_BUCKET}

# =============================================================================
# FEATURE FLAGS (Production Configuration)
# =============================================================================
# Core Analytics Features (All Implemented)
ENABLE_REAL_TIME_ANALYTICS=true
ENABLE_COHORT_ANALYSIS=true
ENABLE_CLV_CALCULATIONS=true
ENABLE_FUNNEL_ANALYSIS=true
ENABLE_ATTRIBUTION_MODELING=true
ENABLE_PREDICTIVE_ANALYTICS=true

# Marketplace Features (All Implemented)
ENABLE_MARKETPLACE_ECOSYSTEM=true
ENABLE_PARTNER_DISCOVERY=true
ENABLE_DATA_PRODUCTS_MARKETPLACE=true
ENABLE_CROSS_BUSINESS_ANALYTICS=true
ENABLE_REVENUE_ATTRIBUTION=true

# Advanced Features
ENABLE_D3_VISUALIZATIONS=true
ENABLE_REAL_TIME_STREAMING=true
ENABLE_SERVER_SENT_EVENTS=true
ENABLE_ISLANDS_ARCHITECTURE=true

# Reporting and Export Features
ENABLE_REPORT_GENERATION=true
ENABLE_REPORT_SCHEDULING=true
ENABLE_DATA_EXPORT=true
ENABLE_CUSTOM_DASHBOARDS=true

# Security and Compliance Features
ENABLE_MULTI_TENANT_ISOLATION=true
ENABLE_ROW_LEVEL_SECURITY=true
ENABLE_GDPR_COMPLIANCE=true
ENABLE_AUDIT_LOGGING=true

# Experimental Features (Disabled in Production)
ENABLE_A_B_TESTING=false
ENABLE_AI_INSIGHTS=false
ENABLE_MOBILE_SDK=false
ENABLE_VOICE_ANALYTICS=false