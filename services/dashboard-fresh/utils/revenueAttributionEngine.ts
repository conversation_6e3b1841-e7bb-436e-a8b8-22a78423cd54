// Revenue Attribution Engine
// Real-time revenue attribution for cross-business events
// Target: <100ms attribution calculation, 99.9% accuracy

import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";

export interface AttributionEvent {
  event_id: string;
  partnership_id: string;
  customer_id: string;
  session_id?: string;
  event_timestamp: Date;
  source_tenant_id: string;
  target_tenant_id: string;
  event_type: string;
  event_data: Record<string, any>;
}

export interface ConversionEvent {
  customer_id: string;
  session_id?: string;
  conversion_timestamp: Date;
  total_revenue: number;
  currency_code: string;
  conversion_type: string;
  conversion_data: Record<string, any>;
}

export interface AttributionResult {
  attribution_id: string;
  event_id: string;
  partnership_id: string;
  customer_id: string;
  session_id?: string;
  touchpoint_sequence: number;
  attribution_weight: number;
  attribution_model: AttributionModel;
  total_revenue: number;
  attributed_revenue: number;
  currency_code: string;
  event_timestamp: Date;
  conversion_timestamp: Date;
  attribution_window_hours: number;
  source_tenant_id: string;
  target_tenant_id: string;
  processing_time_ms: number;
  attribution_metadata: Record<string, any>;
}

export type AttributionModel = 'last_touch' | 'first_touch' | 'linear' | 'time_decay';

export interface AttributionConfig {
  attribution_window_hours: number;
  attribution_model: AttributionModel;
  time_decay_half_life_hours?: number;
  minimum_attribution_weight?: number;
  session_timeout_minutes?: number;
}

export class RevenueAttributionEngine {
  private client: Client;
  private static instance: RevenueAttributionEngine;

  constructor(client: Client) {
    this.client = client;
  }

  public static getInstance(client: Client): RevenueAttributionEngine {
    if (!RevenueAttributionEngine.instance) {
      RevenueAttributionEngine.instance = new RevenueAttributionEngine(client);
    }
    return RevenueAttributionEngine.instance;
  }

  /**
   * Process revenue attribution for a conversion event
   * Target: <100ms processing time
   */
  public async processAttribution(
    conversionEvent: ConversionEvent,
    config: AttributionConfig = {
      attribution_window_hours: 168, // 7 days default
      attribution_model: 'last_touch'
    }
  ): Promise<AttributionResult[]> {
    const startTime = performance.now();

    try {
      // Find relevant touchpoint events within attribution window
      const touchpointEvents = await this.findTouchpointEvents(
        conversionEvent,
        config.attribution_window_hours
      );

      if (touchpointEvents.length === 0) {
        console.log(`No touchpoint events found for customer ${conversionEvent.customer_id}`);
        return [];
      }

      // Calculate attribution weights based on model
      const attributionResults = await this.calculateAttributionWeights(
        touchpointEvents,
        conversionEvent,
        config
      );

      // Store attribution results in database
      await this.storeAttributionResults(attributionResults);

      const processingTime = performance.now() - startTime;

      // Log performance metrics
      if (processingTime > 100) {
        console.warn(`Attribution processing exceeded 100ms target: ${processingTime.toFixed(2)}ms`);
      }

      console.log(`Attribution processed: ${attributionResults.length} touchpoints for customer ${conversionEvent.customer_id} in ${processingTime.toFixed(2)}ms`);

      return attributionResults;

    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error('Revenue attribution processing failed:', {
        error: error.message,
        customer_id: conversionEvent.customer_id,
        processing_time_ms: Math.round(processingTime)
      });
      throw error;
    }
  }

  /**
   * Find touchpoint events within attribution window
   */
  private async findTouchpointEvents(
    conversionEvent: ConversionEvent,
    attributionWindowHours: number
  ): Promise<AttributionEvent[]> {
    const windowStart = new Date(
      conversionEvent.conversion_timestamp.getTime() - (attributionWindowHours * 60 * 60 * 1000)
    );

    const query = `
      SELECT
        CONCAT(time::text, '-', customer_id, '-', event_type) as event_id,
        partnership_id,
        customer_id,
        session_id,
        time as event_timestamp,
        source_tenant_id,
        target_tenant_id,
        event_type,
        event_data
      FROM cross_business_events
      WHERE customer_id = $1
        AND time >= $2
        AND time <= $3
        AND event_type IN ('referral_click', 'referral_view', 'engagement', 'conversion')
      ORDER BY time ASC
    `;

    const result = await this.client.queryObject<AttributionEvent>(query, [
      conversionEvent.customer_id,
      windowStart.toISOString(),
      conversionEvent.conversion_timestamp.toISOString()
    ]);

    return result.rows;
  }

  /**
   * Calculate attribution weights based on selected model
   */
  private async calculateAttributionWeights(
    touchpointEvents: AttributionEvent[],
    conversionEvent: ConversionEvent,
    config: AttributionConfig
  ): Promise<AttributionResult[]> {
    const attributionResults: AttributionResult[] = [];

    for (let i = 0; i < touchpointEvents.length; i++) {
      const event = touchpointEvents[i];
      const touchpointSequence = i + 1;

      let attributionWeight: number;

      switch (config.attribution_model) {
        case 'first_touch':
          attributionWeight = i === 0 ? 1.0 : 0.0;
          break;

        case 'last_touch':
          attributionWeight = i === touchpointEvents.length - 1 ? 1.0 : 0.0;
          break;

        case 'linear':
          attributionWeight = 1.0 / touchpointEvents.length;
          break;

        case 'time_decay':
          attributionWeight = this.calculateTimeDecayWeight(
            event.event_timestamp,
            conversionEvent.conversion_timestamp,
            config.time_decay_half_life_hours || 24
          );
          break;

        default:
          throw new Error(`Unsupported attribution model: ${config.attribution_model}`);
      }

      // Apply minimum weight threshold
      if (config.minimum_attribution_weight && attributionWeight < config.minimum_attribution_weight) {
        attributionWeight = 0;
      }

      if (attributionWeight > 0) {
        const attributedRevenue = this.roundCurrency(conversionEvent.total_revenue * attributionWeight);

        attributionResults.push({
          attribution_id: crypto.randomUUID(),
          event_id: event.event_id,
          partnership_id: event.partnership_id,
          customer_id: event.customer_id,
          session_id: event.session_id,
          touchpoint_sequence,
          attribution_weight,
          attribution_model: config.attribution_model,
          total_revenue: conversionEvent.total_revenue,
          attributed_revenue: attributedRevenue,
          currency_code: conversionEvent.currency_code,
          event_timestamp: event.event_timestamp,
          conversion_timestamp: conversionEvent.conversion_timestamp,
          attribution_window_hours: config.attribution_window_hours,
          source_tenant_id: event.source_tenant_id,
          target_tenant_id: event.target_tenant_id,
          processing_time_ms: 0, // Will be set later
          attribution_metadata: {
            conversion_type: conversionEvent.conversion_type,
            event_type: event.event_type,
            attribution_config: config
          }
        });
      }
    }

    // Normalize weights for time_decay model to ensure they sum to 1.0
    if (config.attribution_model === 'time_decay' && attributionResults.length > 0) {
      this.normalizeAttributionWeights(attributionResults, conversionEvent.total_revenue);
    }

    return attributionResults;
  }

  /**
   * Calculate time decay weight using exponential decay
   */
  private calculateTimeDecayWeight(
    eventTimestamp: Date,
    conversionTimestamp: Date,
    halfLifeHours: number
  ): number {
    const timeDiffHours = (conversionTimestamp.getTime() - eventTimestamp.getTime()) / (1000 * 60 * 60);
    const decayConstant = Math.log(2) / halfLifeHours;
    return Math.exp(-decayConstant * timeDiffHours);
  }

  /**
   * Normalize attribution weights to ensure they sum to 1.0
   */
  private normalizeAttributionWeights(
    attributionResults: AttributionResult[],
    totalRevenue: number
  ): void {
    const totalWeight = attributionResults.reduce((sum, result) => sum + result.attribution_weight, 0);
    
    if (totalWeight > 0) {
      for (const result of attributionResults) {
        result.attribution_weight = result.attribution_weight / totalWeight;
        result.attributed_revenue = this.roundCurrency(totalRevenue * result.attribution_weight);
      }
    }
  }

  /**
   * Store attribution results in database
   */
  private async storeAttributionResults(attributionResults: AttributionResult[]): Promise<void> {
    if (attributionResults.length === 0) return;

    const insertQuery = `
      INSERT INTO revenue_attributions (
        id, event_id, partnership_id, customer_id, session_id,
        touchpoint_sequence, attribution_weight, attribution_model,
        total_revenue, attributed_revenue, currency_code,
        event_timestamp, conversion_timestamp, attribution_window_hours,
        source_tenant_id, target_tenant_id, processing_time_ms,
        attribution_metadata
      ) VALUES ${attributionResults.map((_, i) => 
        `($${i * 18 + 1}, $${i * 18 + 2}, $${i * 18 + 3}, $${i * 18 + 4}, $${i * 18 + 5}, 
         $${i * 18 + 6}, $${i * 18 + 7}, $${i * 18 + 8}, $${i * 18 + 9}, $${i * 18 + 10}, 
         $${i * 18 + 11}, $${i * 18 + 12}, $${i * 18 + 13}, $${i * 18 + 14}, $${i * 18 + 15}, 
         $${i * 18 + 16}, $${i * 18 + 17}, $${i * 18 + 18})`
      ).join(', ')}
    `;

    const values = attributionResults.flatMap(result => [
      result.attribution_id,
      result.event_id,
      result.partnership_id,
      result.customer_id,
      result.session_id,
      result.touchpoint_sequence,
      result.attribution_weight,
      result.attribution_model,
      result.total_revenue,
      result.attributed_revenue,
      result.currency_code,
      result.event_timestamp.toISOString(),
      result.conversion_timestamp.toISOString(),
      result.attribution_window_hours,
      result.source_tenant_id,
      result.target_tenant_id,
      result.processing_time_ms,
      JSON.stringify(result.attribution_metadata)
    ]);

    await this.client.queryArray(insertQuery, values);
  }

  /**
   * Get attribution summary for a partnership
   */
  public async getAttributionSummary(
    partnershipId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    total_attributed_revenue: number;
    total_conversions: number;
    attribution_model_breakdown: Record<AttributionModel, number>;
    top_touchpoint_types: Array<{ event_type: string; attributed_revenue: number }>;
  }> {
    const query = `
      SELECT 
        SUM(attributed_revenue) as total_attributed_revenue,
        COUNT(DISTINCT customer_id) as total_conversions,
        attribution_model,
        attribution_metadata->>'event_type' as event_type,
        SUM(attributed_revenue) as event_type_revenue
      FROM revenue_attributions
      WHERE partnership_id = $1
        AND conversion_timestamp >= $2
        AND conversion_timestamp <= $3
      GROUP BY attribution_model, attribution_metadata->>'event_type'
      ORDER BY event_type_revenue DESC
    `;

    const result = await this.client.queryObject(query, [
      partnershipId,
      startDate.toISOString(),
      endDate.toISOString()
    ]);

    // Process results into summary format
    const summary = {
      total_attributed_revenue: 0,
      total_conversions: 0,
      attribution_model_breakdown: {} as Record<AttributionModel, number>,
      top_touchpoint_types: [] as Array<{ event_type: string; attributed_revenue: number }>
    };

    for (const row of result.rows) {
      summary.total_attributed_revenue += Number(row.total_attributed_revenue || 0);
      summary.total_conversions = Math.max(summary.total_conversions, Number(row.total_conversions || 0));
      
      const model = row.attribution_model as AttributionModel;
      summary.attribution_model_breakdown[model] = (summary.attribution_model_breakdown[model] || 0) + Number(row.event_type_revenue || 0);
      
      if (row.event_type) {
        summary.top_touchpoint_types.push({
          event_type: row.event_type as string,
          attributed_revenue: Number(row.event_type_revenue || 0)
        });
      }
    }

    return summary;
  }

  /**
   * Round currency amounts to 2 decimal places
   */
  private roundCurrency(amount: number): number {
    return Math.round(amount * 100) / 100;
  }

  /**
   * Batch process multiple conversion events
   */
  public async batchProcessAttribution(
    conversionEvents: ConversionEvent[],
    config: AttributionConfig
  ): Promise<AttributionResult[]> {
    const startTime = performance.now();
    
    const allResults = await Promise.all(
      conversionEvents.map(event => this.processAttribution(event, config))
    );

    const flatResults = allResults.flat();
    const totalProcessingTime = performance.now() - startTime;
    const avgProcessingTime = totalProcessingTime / conversionEvents.length;

    console.log(`Batch attribution completed: ${conversionEvents.length} conversions, ${flatResults.length} attributions in ${totalProcessingTime.toFixed(2)}ms (avg: ${avgProcessingTime.toFixed(2)}ms per conversion)`);

    return flatResults;
  }
}
