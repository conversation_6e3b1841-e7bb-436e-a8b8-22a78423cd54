// Tier-Based Access Control System
// Feature gating and access control based on customer subscription tier
// Target: <10ms access check, 100% security enforcement

import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";

export type CustomerTier = 'core' | 'advanced' | 'enterprise' | 'strategic';

export interface TierFeature {
  id: string;
  tier_name: CustomerTier;
  feature_name: string;
  feature_category: string;
  is_enabled: boolean;
  usage_limit?: number;
  rate_limit_per_hour?: number;
  base_price_monthly?: number;
  overage_price_per_unit?: number;
  feature_description?: string;
  feature_metadata?: Record<string, any>;
}

export interface AccessCheckResult {
  allowed: boolean;
  tier: CustomerTier;
  feature: string;
  reason?: string;
  usage_remaining?: number;
  rate_limit_remaining?: number;
  upgrade_required?: CustomerTier;
  processing_time_ms: number;
}

export interface UsageTracker {
  tenant_id: string;
  feature_name: string;
  usage_count: number;
  rate_limit_count: number;
  last_reset: Date;
  last_usage: Date;
}

// Feature definitions by tier
export const TIER_FEATURES: Record<CustomerTier, Record<string, TierFeature>> = {
  core: {
    basic_analytics: {
      id: 'core_basic_analytics',
      tier_name: 'core',
      feature_name: 'basic_analytics',
      feature_category: 'analytics',
      is_enabled: true,
      usage_limit: 100, // 100 queries per month
      rate_limit_per_hour: 10,
      feature_description: 'Basic analytics dashboard and reports'
    }
  },
  advanced: {
    basic_analytics: {
      id: 'advanced_basic_analytics',
      tier_name: 'advanced',
      feature_name: 'basic_analytics',
      feature_category: 'analytics',
      is_enabled: true,
      usage_limit: 1000,
      rate_limit_per_hour: 50,
      feature_description: 'Enhanced analytics with more data points'
    },
    partner_discovery: {
      id: 'advanced_partner_discovery',
      tier_name: 'advanced',
      feature_name: 'partner_discovery',
      feature_category: 'marketplace',
      is_enabled: true,
      usage_limit: 100, // 100 partner searches per month
      rate_limit_per_hour: 20,
      feature_description: 'AI-powered partner discovery and compatibility scoring'
    },
    partnership_requests: {
      id: 'advanced_partnership_requests',
      tier_name: 'advanced',
      feature_name: 'partnership_requests',
      feature_category: 'marketplace',
      is_enabled: true,
      usage_limit: 10, // 10 partnership requests per month
      rate_limit_per_hour: 2,
      feature_description: 'Send partnership requests to compatible businesses'
    },
    transaction_fees: {
      id: 'advanced_transaction_fees',
      tier_name: 'advanced',
      feature_name: 'transaction_fees',
      feature_category: 'revenue',
      is_enabled: true,
      feature_description: '5% transaction fee on marketplace revenue'
    }
  },
  enterprise: {
    basic_analytics: {
      id: 'enterprise_basic_analytics',
      tier_name: 'enterprise',
      feature_name: 'basic_analytics',
      feature_category: 'analytics',
      is_enabled: true,
      usage_limit: 10000,
      rate_limit_per_hour: 200,
      feature_description: 'Advanced analytics with custom dashboards'
    },
    partner_discovery: {
      id: 'enterprise_partner_discovery',
      tier_name: 'enterprise',
      feature_name: 'partner_discovery',
      feature_category: 'marketplace',
      is_enabled: true,
      usage_limit: 1000,
      rate_limit_per_hour: 100,
      feature_description: 'Unlimited partner discovery with advanced filters'
    },
    partnership_requests: {
      id: 'enterprise_partnership_requests',
      tier_name: 'enterprise',
      feature_name: 'partnership_requests',
      feature_category: 'marketplace',
      is_enabled: true,
      usage_limit: 100,
      rate_limit_per_hour: 10,
      feature_description: 'Unlimited partnership requests and management'
    },
    revenue_sharing: {
      id: 'enterprise_revenue_sharing',
      tier_name: 'enterprise',
      feature_name: 'revenue_sharing',
      feature_category: 'revenue',
      is_enabled: true,
      feature_description: 'Advanced revenue sharing and attribution'
    },
    collaborative_analytics: {
      id: 'enterprise_collaborative_analytics',
      tier_name: 'enterprise',
      feature_name: 'collaborative_analytics',
      feature_category: 'analytics',
      is_enabled: true,
      usage_limit: 1000,
      rate_limit_per_hour: 50,
      feature_description: 'Shared analytics dashboards with partners'
    },
    data_products: {
      id: 'enterprise_data_products',
      tier_name: 'enterprise',
      feature_name: 'data_products',
      feature_category: 'marketplace',
      is_enabled: true,
      usage_limit: 50,
      rate_limit_per_hour: 10,
      feature_description: 'Access to marketplace data products'
    },
    transaction_fees: {
      id: 'enterprise_transaction_fees',
      tier_name: 'enterprise',
      feature_name: 'transaction_fees',
      feature_category: 'revenue',
      is_enabled: true,
      feature_description: '3% transaction fee on marketplace revenue'
    }
  },
  strategic: {
    basic_analytics: {
      id: 'strategic_basic_analytics',
      tier_name: 'strategic',
      feature_name: 'basic_analytics',
      feature_category: 'analytics',
      is_enabled: true,
      feature_description: 'Unlimited analytics with custom algorithms'
    },
    partner_discovery: {
      id: 'strategic_partner_discovery',
      tier_name: 'strategic',
      feature_name: 'partner_discovery',
      feature_category: 'marketplace',
      is_enabled: true,
      feature_description: 'Unlimited partner discovery with custom algorithms'
    },
    partnership_requests: {
      id: 'strategic_partnership_requests',
      tier_name: 'strategic',
      feature_name: 'partnership_requests',
      feature_category: 'marketplace',
      is_enabled: true,
      feature_description: 'Unlimited partnership management'
    },
    revenue_sharing: {
      id: 'strategic_revenue_sharing',
      tier_name: 'strategic',
      feature_name: 'revenue_sharing',
      feature_category: 'revenue',
      is_enabled: true,
      feature_description: 'Custom revenue sharing models'
    },
    collaborative_analytics: {
      id: 'strategic_collaborative_analytics',
      tier_name: 'strategic',
      feature_name: 'collaborative_analytics',
      feature_category: 'analytics',
      is_enabled: true,
      feature_description: 'Unlimited collaborative analytics'
    },
    data_products: {
      id: 'strategic_data_products',
      tier_name: 'strategic',
      feature_name: 'data_products',
      feature_category: 'marketplace',
      is_enabled: true,
      feature_description: 'Create and sell data products'
    },
    white_label: {
      id: 'strategic_white_label',
      tier_name: 'strategic',
      feature_name: 'white_label',
      feature_category: 'platform',
      is_enabled: true,
      feature_description: 'White-label marketplace platform'
    },
    custom_algorithms: {
      id: 'strategic_custom_algorithms',
      tier_name: 'strategic',
      feature_name: 'custom_algorithms',
      feature_category: 'analytics',
      is_enabled: true,
      feature_description: 'Custom analytics algorithms and models'
    },
    priority_support: {
      id: 'strategic_priority_support',
      tier_name: 'strategic',
      feature_name: 'priority_support',
      feature_category: 'support',
      is_enabled: true,
      feature_description: '24/7 priority support with dedicated account manager'
    },
    transaction_fees: {
      id: 'strategic_transaction_fees',
      tier_name: 'strategic',
      feature_name: 'transaction_fees',
      feature_category: 'revenue',
      is_enabled: true,
      feature_description: '1% transaction fee on marketplace revenue'
    }
  }
};

export class TierAccessControl {
  private client: Client;
  private usageCache: Map<string, UsageTracker> = new Map();
  private static instance: TierAccessControl;

  constructor(client: Client) {
    this.client = client;
  }

  public static getInstance(client: Client): TierAccessControl {
    if (!TierAccessControl.instance) {
      TierAccessControl.instance = new TierAccessControl(client);
    }
    return TierAccessControl.instance;
  }

  /**
   * Check if a tenant has access to a specific feature
   * Target: <10ms response time
   */
  public async checkAccess(
    tenantId: string,
    customerTier: CustomerTier,
    featureName: string
  ): Promise<AccessCheckResult> {
    const startTime = performance.now();

    try {
      // Get feature definition for tier
      const tierFeatures = TIER_FEATURES[customerTier];
      const feature = tierFeatures[featureName];

      if (!feature) {
        const processingTime = performance.now() - startTime;
        return {
          allowed: false,
          tier: customerTier,
          feature: featureName,
          reason: 'Feature not available for this tier',
          upgrade_required: this.getMinimumTierForFeature(featureName),
          processing_time_ms: Math.round(processingTime)
        };
      }

      if (!feature.is_enabled) {
        const processingTime = performance.now() - startTime;
        return {
          allowed: false,
          tier: customerTier,
          feature: featureName,
          reason: 'Feature is disabled',
          processing_time_ms: Math.round(processingTime)
        };
      }

      // Check usage limits
      const usageCheck = await this.checkUsageLimits(tenantId, feature);
      if (!usageCheck.allowed) {
        const processingTime = performance.now() - startTime;
        return {
          ...usageCheck,
          processing_time_ms: Math.round(processingTime)
        };
      }

      // Check rate limits
      const rateLimitCheck = await this.checkRateLimits(tenantId, feature);
      if (!rateLimitCheck.allowed) {
        const processingTime = performance.now() - startTime;
        return {
          ...rateLimitCheck,
          processing_time_ms: Math.round(processingTime)
        };
      }

      const processingTime = performance.now() - startTime;

      return {
        allowed: true,
        tier: customerTier,
        feature: featureName,
        usage_remaining: usageCheck.usage_remaining,
        rate_limit_remaining: rateLimitCheck.rate_limit_remaining,
        processing_time_ms: Math.round(processingTime)
      };

    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error('Access check failed:', error);
      
      return {
        allowed: false,
        tier: customerTier,
        feature: featureName,
        reason: `Access check error: ${error.message}`,
        processing_time_ms: Math.round(processingTime)
      };
    }
  }

  /**
   * Record feature usage for a tenant
   */
  public async recordUsage(
    tenantId: string,
    featureName: string,
    usageCount: number = 1
  ): Promise<void> {
    const cacheKey = `${tenantId}:${featureName}`;
    const now = new Date();

    // Update cache
    const existing = this.usageCache.get(cacheKey);
    if (existing) {
      existing.usage_count += usageCount;
      existing.rate_limit_count += usageCount;
      existing.last_usage = now;
    } else {
      this.usageCache.set(cacheKey, {
        tenant_id: tenantId,
        feature_name: featureName,
        usage_count: usageCount,
        rate_limit_count: usageCount,
        last_reset: now,
        last_usage: now
      });
    }

    // Persist to database (async, don't wait)
    this.persistUsageToDatabase(tenantId, featureName, usageCount).catch(error => {
      console.error('Failed to persist usage to database:', error);
    });
  }

  /**
   * Get all features available for a tier
   */
  public getFeaturesForTier(customerTier: CustomerTier): TierFeature[] {
    return Object.values(TIER_FEATURES[customerTier]);
  }

  /**
   * Get minimum tier required for a feature
   */
  public getMinimumTierForFeature(featureName: string): CustomerTier | undefined {
    const tiers: CustomerTier[] = ['core', 'advanced', 'enterprise', 'strategic'];
    
    for (const tier of tiers) {
      if (TIER_FEATURES[tier][featureName]?.is_enabled) {
        return tier;
      }
    }
    
    return undefined;
  }

  /**
   * Check usage limits for a feature
   */
  private async checkUsageLimits(
    tenantId: string,
    feature: TierFeature
  ): Promise<{ allowed: boolean; usage_remaining?: number; reason?: string }> {
    if (!feature.usage_limit) {
      return { allowed: true }; // Unlimited usage
    }

    const cacheKey = `${tenantId}:${feature.feature_name}`;
    const usage = this.usageCache.get(cacheKey);

    if (!usage) {
      return { allowed: true, usage_remaining: feature.usage_limit };
    }

    // Reset monthly usage if needed
    const now = new Date();
    const monthsSinceReset = (now.getTime() - usage.last_reset.getTime()) / (1000 * 60 * 60 * 24 * 30);
    
    if (monthsSinceReset >= 1) {
      usage.usage_count = 0;
      usage.last_reset = now;
    }

    const remaining = feature.usage_limit - usage.usage_count;
    
    if (remaining <= 0) {
      return {
        allowed: false,
        usage_remaining: 0,
        reason: `Monthly usage limit of ${feature.usage_limit} exceeded`
      };
    }

    return { allowed: true, usage_remaining: remaining };
  }

  /**
   * Check rate limits for a feature
   */
  private async checkRateLimits(
    tenantId: string,
    feature: TierFeature
  ): Promise<{ allowed: boolean; rate_limit_remaining?: number; reason?: string }> {
    if (!feature.rate_limit_per_hour) {
      return { allowed: true }; // No rate limit
    }

    const cacheKey = `${tenantId}:${feature.feature_name}`;
    const usage = this.usageCache.get(cacheKey);

    if (!usage) {
      return { allowed: true, rate_limit_remaining: feature.rate_limit_per_hour };
    }

    // Reset hourly rate limit if needed
    const now = new Date();
    const hoursSinceLastUsage = (now.getTime() - usage.last_usage.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceLastUsage >= 1) {
      usage.rate_limit_count = 0;
    }

    const remaining = feature.rate_limit_per_hour - usage.rate_limit_count;
    
    if (remaining <= 0) {
      return {
        allowed: false,
        rate_limit_remaining: 0,
        reason: `Hourly rate limit of ${feature.rate_limit_per_hour} exceeded`
      };
    }

    return { allowed: true, rate_limit_remaining: remaining };
  }

  /**
   * Persist usage data to database
   */
  private async persistUsageToDatabase(
    tenantId: string,
    featureName: string,
    usageCount: number
  ): Promise<void> {
    // This would typically update a usage tracking table
    // For now, we'll just log it
    console.log(`Usage recorded: ${tenantId} used ${featureName} ${usageCount} times`);
  }
}

// Export singleton instance factory
export function createTierAccessControl(client: Client): TierAccessControl {
  return TierAccessControl.getInstance(client);
}
