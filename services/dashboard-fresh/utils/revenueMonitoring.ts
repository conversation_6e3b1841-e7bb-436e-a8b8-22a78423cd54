// Revenue Stream Monitoring and Alerting System
// Real-time monitoring for $335K+ ARR potential revenue streams
// Target: <1% error rate, <100ms response times, 99.9% uptime

import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";

export interface RevenueMetrics {
  timestamp: Date;
  metric_type: 'transaction_fee' | 'data_product' | 'premium_matching' | 'volume_discount';
  metric_name: string;
  metric_value: number;
  tenant_id?: string;
  processing_time_ms: number;
  success: boolean;
  error_message?: string;
  metadata?: Record<string, unknown>;
}

export interface AlertThreshold {
  metric_type: string;
  metric_name: string;
  threshold_type: 'min' | 'max' | 'percentage';
  threshold_value: number;
  alert_level: 'warning' | 'critical';
  notification_channels: string[];
}

export interface RevenueAlert {
  id: string;
  alert_type: string;
  severity: 'warning' | 'critical';
  message: string;
  metric_value: number;
  threshold_value: number;
  tenant_id?: string;
  created_at: Date;
  resolved_at?: Date;
  metadata?: Record<string, unknown>;
}

export class RevenueMonitoringService {
  private client: Client;
  private static instance: RevenueMonitoringService;
  private alertThresholds: AlertThreshold[] = [];

  constructor(client: Client) {
    this.client = client;
    this.initializeDefaultThresholds();
  }

  public static getInstance(client: Client): RevenueMonitoringService {
    if (!RevenueMonitoringService.instance) {
      RevenueMonitoringService.instance = new RevenueMonitoringService(client);
    }
    return RevenueMonitoringService.instance;
  }

  /**
   * Initialize default monitoring thresholds
   */
  private initializeDefaultThresholds(): void {
    this.alertThresholds = [
      // Performance thresholds
      {
        metric_type: 'transaction_fee',
        metric_name: 'processing_time_ms',
        threshold_type: 'max',
        threshold_value: 50,
        alert_level: 'warning',
        notification_channels: ['email', 'slack']
      },
      {
        metric_type: 'data_product',
        metric_name: 'processing_time_ms',
        threshold_type: 'max',
        threshold_value: 100,
        alert_level: 'warning',
        notification_channels: ['email', 'slack']
      },
      {
        metric_type: 'premium_matching',
        metric_name: 'processing_time_ms',
        threshold_type: 'max',
        threshold_value: 100,
        alert_level: 'critical',
        notification_channels: ['email', 'slack', 'pagerduty']
      },
      
      // Error rate thresholds
      {
        metric_type: 'transaction_fee',
        metric_name: 'error_rate',
        threshold_type: 'max',
        threshold_value: 1.0, // 1%
        alert_level: 'critical',
        notification_channels: ['email', 'slack', 'pagerduty']
      },
      {
        metric_type: 'data_product',
        metric_name: 'error_rate',
        threshold_type: 'max',
        threshold_value: 0.5, // 0.5%
        alert_level: 'warning',
        notification_channels: ['email', 'slack']
      },
      
      // Revenue thresholds
      {
        metric_type: 'volume_discount',
        metric_name: 'discount_application_rate',
        threshold_type: 'min',
        threshold_value: 80.0, // 80% of eligible transactions should get discounts
        alert_level: 'warning',
        notification_channels: ['email']
      },
      {
        metric_type: 'premium_matching',
        metric_name: 'success_rate',
        threshold_type: 'min',
        threshold_value: 75.0, // 75% success rate
        alert_level: 'warning',
        notification_channels: ['email', 'slack']
      }
    ];
  }

  /**
   * Record a revenue metric
   */
  public async recordMetric(metric: Omit<RevenueMetrics, 'timestamp'>): Promise<void> {
    const startTime = performance.now();

    try {
      const fullMetric: RevenueMetrics = {
        ...metric,
        timestamp: new Date()
      };

      // Store metric in database
      await this.storeMetric(fullMetric);

      // Check for alert conditions
      await this.checkAlertThresholds(fullMetric);

      const processingTime = performance.now() - startTime;
      console.log(`Revenue metric recorded in ${processingTime.toFixed(2)}ms`);

    } catch (error) {
      console.error('Failed to record revenue metric:', error);
    }
  }

  /**
   * Store metric in database
   */
  private async storeMetric(metric: RevenueMetrics): Promise<void> {
    const query = `
      INSERT INTO revenue_metrics (
        timestamp, metric_type, metric_name, metric_value, tenant_id,
        processing_time_ms, success, error_message, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `;

    await this.client.queryArray(query, [
      metric.timestamp.toISOString(),
      metric.metric_type,
      metric.metric_name,
      metric.metric_value,
      metric.tenant_id,
      metric.processing_time_ms,
      metric.success,
      metric.error_message,
      JSON.stringify(metric.metadata || {})
    ]);
  }

  /**
   * Check if metric violates any alert thresholds
   */
  private async checkAlertThresholds(metric: RevenueMetrics): Promise<void> {
    for (const threshold of this.alertThresholds) {
      if (threshold.metric_type === metric.metric_type && 
          threshold.metric_name === metric.metric_name) {
        
        const violatesThreshold = this.evaluateThreshold(metric.metric_value, threshold);
        
        if (violatesThreshold) {
          await this.createAlert({
            id: crypto.randomUUID(),
            alert_type: `${threshold.metric_type}_${threshold.metric_name}`,
            severity: threshold.alert_level,
            message: `${threshold.metric_name} ${threshold.threshold_type} threshold violated: ${metric.metric_value} vs ${threshold.threshold_value}`,
            metric_value: metric.metric_value,
            threshold_value: threshold.threshold_value,
            tenant_id: metric.tenant_id,
            created_at: new Date(),
            metadata: {
              metric_type: metric.metric_type,
              processing_time_ms: metric.processing_time_ms,
              notification_channels: threshold.notification_channels
            }
          });
        }
      }
    }
  }

  /**
   * Evaluate if a metric value violates a threshold
   */
  private evaluateThreshold(value: number, threshold: AlertThreshold): boolean {
    switch (threshold.threshold_type) {
      case 'min':
        return value < threshold.threshold_value;
      case 'max':
        return value > threshold.threshold_value;
      case 'percentage':
        // For percentage-based thresholds, implement custom logic
        return false;
      default:
        return false;
    }
  }

  /**
   * Create an alert
   */
  private async createAlert(alert: RevenueAlert): Promise<void> {
    const query = `
      INSERT INTO revenue_alerts (
        id, alert_type, severity, message, metric_value, threshold_value,
        tenant_id, created_at, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `;

    await this.client.queryArray(query, [
      alert.id,
      alert.alert_type,
      alert.severity,
      alert.message,
      alert.metric_value,
      alert.threshold_value,
      alert.tenant_id,
      alert.created_at.toISOString(),
      JSON.stringify(alert.metadata || {})
    ]);

    // Send notifications
    await this.sendNotifications(alert);
  }

  /**
   * Send alert notifications
   */
  private async sendNotifications(alert: RevenueAlert): Promise<void> {
    const channels = alert.metadata?.notification_channels as string[] || [];
    
    for (const channel of channels) {
      try {
        switch (channel) {
          case 'email':
            await this.sendEmailNotification(alert);
            break;
          case 'slack':
            await this.sendSlackNotification(alert);
            break;
          case 'pagerduty':
            await this.sendPagerDutyNotification(alert);
            break;
        }
      } catch (error) {
        console.error(`Failed to send ${channel} notification:`, error);
      }
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(alert: RevenueAlert): Promise<void> {
    // Implementation would integrate with email service (SendGrid, AWS SES, etc.)
    console.log(`EMAIL ALERT: ${alert.severity.toUpperCase()} - ${alert.message}`);
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(alert: RevenueAlert): Promise<void> {
    // Implementation would integrate with Slack API
    console.log(`SLACK ALERT: ${alert.severity.toUpperCase()} - ${alert.message}`);
  }

  /**
   * Send PagerDuty notification
   */
  private async sendPagerDutyNotification(alert: RevenueAlert): Promise<void> {
    // Implementation would integrate with PagerDuty API
    console.log(`PAGERDUTY ALERT: ${alert.severity.toUpperCase()} - ${alert.message}`);
  }

  /**
   * Get revenue metrics dashboard data
   */
  public async getRevenueDashboard(
    startDate: Date,
    endDate: Date,
    tenantId?: string
  ): Promise<{
    transaction_fees: any;
    data_products: any;
    premium_matching: any;
    volume_discounts: any;
    alerts: RevenueAlert[];
  }> {
    const startTime = performance.now();

    try {
      // Get metrics by type
      const [transactionFees, dataProducts, premiumMatching, volumeDiscounts, alerts] = await Promise.all([
        this.getMetricsByType('transaction_fee', startDate, endDate, tenantId),
        this.getMetricsByType('data_product', startDate, endDate, tenantId),
        this.getMetricsByType('premium_matching', startDate, endDate, tenantId),
        this.getMetricsByType('volume_discount', startDate, endDate, tenantId),
        this.getActiveAlerts(tenantId)
      ]);

      const processingTime = performance.now() - startTime;
      console.log(`Revenue dashboard data retrieved in ${processingTime.toFixed(2)}ms`);

      return {
        transaction_fees: transactionFees,
        data_products: dataProducts,
        premium_matching: premiumMatching,
        volume_discounts: volumeDiscounts,
        alerts
      };

    } catch (error) {
      console.error('Failed to get revenue dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get metrics by type
   */
  private async getMetricsByType(
    metricType: string,
    startDate: Date,
    endDate: Date,
    tenantId?: string
  ): Promise<any> {
    let query = `
      SELECT 
        metric_name,
        AVG(metric_value) as avg_value,
        MAX(metric_value) as max_value,
        MIN(metric_value) as min_value,
        COUNT(*) as total_count,
        COUNT(*) FILTER (WHERE success = true) as success_count,
        AVG(processing_time_ms) as avg_processing_time
      FROM revenue_metrics
      WHERE metric_type = $1
        AND timestamp >= $2
        AND timestamp <= $3
    `;

    const params = [metricType, startDate.toISOString(), endDate.toISOString()];

    if (tenantId) {
      query += ` AND tenant_id = $4`;
      params.push(tenantId);
    }

    query += ` GROUP BY metric_name ORDER BY metric_name`;

    const result = await this.client.queryObject(query, params);
    return result.rows;
  }

  /**
   * Get active alerts
   */
  private async getActiveAlerts(tenantId?: string): Promise<RevenueAlert[]> {
    let query = `
      SELECT * FROM revenue_alerts
      WHERE resolved_at IS NULL
    `;

    const params: string[] = [];

    if (tenantId) {
      query += ` AND tenant_id = $1`;
      params.push(tenantId);
    }

    query += ` ORDER BY created_at DESC LIMIT 50`;

    const result = await this.client.queryObject<RevenueAlert>(query, params);
    return result.rows;
  }

  /**
   * Resolve an alert
   */
  public async resolveAlert(alertId: string): Promise<void> {
    const query = `
      UPDATE revenue_alerts
      SET resolved_at = NOW()
      WHERE id = $1
    `;

    await this.client.queryArray(query, [alertId]);
  }
}
