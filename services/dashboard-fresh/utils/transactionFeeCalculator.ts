// Transaction Fee Calculation System
// High-performance revenue calculation engine for marketplace transactions
// Target: <50ms calculation time, 99.9% accuracy

export interface TransactionFeeCalculation {
  partnership_id: string;
  attributed_revenue: number;
  currency_code: string;
  customer_tier: CustomerTier;
  base_fee_percentage: number;
  volume_discount_percentage: number;
  final_fee_percentage: number;
  platform_commission: number;
  partner_payout: number;
  attribution_model: AttributionModel;
  attribution_confidence: number;
  calculation_timestamp: Date;
  processing_time_ms: number;
}

export type CustomerTier = 'core' | 'advanced' | 'enterprise' | 'strategic';
export type AttributionModel = 'last_touch' | 'first_touch' | 'linear' | 'time_decay';

export interface VolumeDiscount {
  threshold: number;
  discount_percentage: number;
  applies_to_tiers: CustomerTier[];
}

export interface TierFeeStructure {
  tier: CustomerTier;
  base_fee_percentage: number;
  features_enabled: string[];
  monthly_transaction_limit?: number;
}

// Fee structure configuration based on revenue model
export const FEE_STRUCTURE: Record<CustomerTier, TierFeeStructure> = {
  core: {
    tier: 'core',
    base_fee_percentage: 0, // No marketplace access
    features_enabled: [],
    monthly_transaction_limit: 0
  },
  advanced: {
    tier: 'advanced',
    base_fee_percentage: 5.0, // 5% transaction fee
    features_enabled: ['partner_discovery', 'basic_analytics', 'partnership_requests'],
    monthly_transaction_limit: 100
  },
  enterprise: {
    tier: 'enterprise',
    base_fee_percentage: 3.0, // 3% transaction fee
    features_enabled: ['full_marketplace', 'revenue_sharing', 'collaborative_analytics', 'data_products'],
    monthly_transaction_limit: 1000
  },
  strategic: {
    tier: 'strategic',
    base_fee_percentage: 1.0, // 1% transaction fee
    features_enabled: ['white_label', 'custom_algorithms', 'unlimited_access', 'priority_support'],
    monthly_transaction_limit: undefined // Unlimited
  }
};

// Volume discount structure
export const VOLUME_DISCOUNTS: VolumeDiscount[] = [
  {
    threshold: 100000, // $100K monthly volume
    discount_percentage: 10,
    applies_to_tiers: ['advanced', 'enterprise', 'strategic']
  },
  {
    threshold: 500000, // $500K monthly volume
    discount_percentage: 20,
    applies_to_tiers: ['enterprise', 'strategic']
  },
  {
    threshold: 1000000, // $1M monthly volume
    discount_percentage: 30,
    applies_to_tiers: ['strategic']
  }
];

export class TransactionFeeCalculator {
  private static instance: TransactionFeeCalculator;
  
  public static getInstance(): TransactionFeeCalculator {
    if (!TransactionFeeCalculator.instance) {
      TransactionFeeCalculator.instance = new TransactionFeeCalculator();
    }
    return TransactionFeeCalculator.instance;
  }

  /**
   * Calculate transaction fees with volume discounts
   * Target: <50ms processing time
   */
  public async calculateTransactionFee(
    partnershipId: string,
    attributedRevenue: number,
    customerTier: CustomerTier,
    monthlyVolume: number = 0,
    attributionModel: AttributionModel = 'last_touch',
    attributionConfidence: number = 100,
    currencyCode: string = 'USD'
  ): Promise<TransactionFeeCalculation> {
    const startTime = performance.now();

    try {
      // Validate inputs
      this.validateInputs(attributedRevenue, customerTier, attributionConfidence);

      // Get base fee structure
      const tierStructure = FEE_STRUCTURE[customerTier];
      if (!tierStructure) {
        throw new Error(`Invalid customer tier: ${customerTier}`);
      }

      // Calculate volume discount
      const volumeDiscount = this.calculateVolumeDiscount(monthlyVolume, customerTier);

      // Calculate final fee percentage (discount is a percentage of the base fee)
      const baseFeePercentage = tierStructure.base_fee_percentage;
      const discountAmount = (baseFeePercentage * volumeDiscount) / 100;
      const finalFeePercentage = Math.max(0, baseFeePercentage - discountAmount);

      // Calculate amounts
      const platformCommission = this.roundCurrency(
        (attributedRevenue * finalFeePercentage) / 100
      );
      const partnerPayout = this.roundCurrency(attributedRevenue - platformCommission);

      // Validate calculation
      this.validateCalculation(attributedRevenue, platformCommission, partnerPayout);

      const processingTime = performance.now() - startTime;

      const calculation: TransactionFeeCalculation = {
        partnership_id: partnershipId,
        attributed_revenue: attributedRevenue,
        currency_code: currencyCode,
        customer_tier: customerTier,
        base_fee_percentage: baseFeePercentage,
        volume_discount_percentage: volumeDiscount,
        final_fee_percentage: finalFeePercentage,
        platform_commission: platformCommission,
        partner_payout: partnerPayout,
        attribution_model: attributionModel,
        attribution_confidence: attributionConfidence,
        calculation_timestamp: new Date(),
        processing_time_ms: Math.round(processingTime)
      };

      // Log performance metrics
      if (processingTime > 50) {
        console.warn(`Transaction fee calculation exceeded 50ms target: ${processingTime.toFixed(2)}ms`);
      }

      return calculation;

    } catch (error) {
      const processingTime = performance.now() - startTime;
      console.error('Transaction fee calculation failed:', {
        error: error.message,
        partnershipId,
        attributedRevenue,
        customerTier,
        processingTime: Math.round(processingTime)
      });
      throw error;
    }
  }

  /**
   * Calculate volume discount based on monthly transaction volume
   */
  private calculateVolumeDiscount(monthlyVolume: number, customerTier: CustomerTier): number {
    // Core tier doesn't get volume discounts
    if (customerTier === 'core') {
      return 0;
    }

    // Find applicable discounts for this tier
    const applicableDiscounts = VOLUME_DISCOUNTS.filter(discount =>
      discount.applies_to_tiers.includes(customerTier) &&
      monthlyVolume >= discount.threshold
    );

    // Return highest applicable discount
    if (applicableDiscounts.length === 0) {
      return 0;
    }

    return Math.max(...applicableDiscounts.map(d => d.discount_percentage));
  }

  /**
   * Validate calculation inputs
   */
  private validateInputs(
    attributedRevenue: number, 
    customerTier: CustomerTier, 
    attributionConfidence: number
  ): void {
    if (attributedRevenue < 0) {
      throw new Error('Attributed revenue must be non-negative');
    }

    if (!Object.keys(FEE_STRUCTURE).includes(customerTier)) {
      throw new Error(`Invalid customer tier: ${customerTier}`);
    }

    if (attributionConfidence < 0 || attributionConfidence > 100) {
      throw new Error('Attribution confidence must be between 0 and 100');
    }
  }

  /**
   * Validate calculation results
   */
  private validateCalculation(
    attributedRevenue: number, 
    platformCommission: number, 
    partnerPayout: number
  ): void {
    const total = platformCommission + partnerPayout;
    const difference = Math.abs(total - attributedRevenue);
    
    // Allow for small rounding differences (1 cent)
    if (difference > 0.01) {
      throw new Error(
        `Calculation validation failed: ${platformCommission} + ${partnerPayout} = ${total} ≠ ${attributedRevenue}`
      );
    }
  }

  /**
   * Round currency amounts to 2 decimal places
   */
  private roundCurrency(amount: number): number {
    return Math.round(amount * 100) / 100;
  }

  /**
   * Get tier feature access for access control
   */
  public getTierFeatures(customerTier: CustomerTier): string[] {
    const tierStructure = FEE_STRUCTURE[customerTier];
    return tierStructure ? tierStructure.features_enabled : [];
  }

  /**
   * Check if tier has access to specific feature
   */
  public hasFeatureAccess(customerTier: CustomerTier, featureName: string): boolean {
    const features = this.getTierFeatures(customerTier);
    return features.includes(featureName);
  }

  /**
   * Get monthly transaction limit for tier
   */
  public getTransactionLimit(customerTier: CustomerTier): number | undefined {
    const tierStructure = FEE_STRUCTURE[customerTier];
    return tierStructure ? tierStructure.monthly_transaction_limit : undefined;
  }

  /**
   * Calculate projected monthly revenue for tier upgrade analysis
   */
  public calculateProjectedRevenue(
    currentTier: CustomerTier,
    targetTier: CustomerTier,
    monthlyVolume: number
  ): { currentFees: number; targetFees: number; savings: number } {
    const currentDiscount = this.calculateVolumeDiscount(monthlyVolume, currentTier);
    const targetDiscount = this.calculateVolumeDiscount(monthlyVolume, targetTier);

    const currentFeeRate = Math.max(0, FEE_STRUCTURE[currentTier].base_fee_percentage - currentDiscount);
    const targetFeeRate = Math.max(0, FEE_STRUCTURE[targetTier].base_fee_percentage - targetDiscount);

    const currentFees = (monthlyVolume * currentFeeRate) / 100;
    const targetFees = (monthlyVolume * targetFeeRate) / 100;
    const savings = currentFees - targetFees;

    return {
      currentFees: this.roundCurrency(currentFees),
      targetFees: this.roundCurrency(targetFees),
      savings: this.roundCurrency(savings)
    };
  }

  /**
   * Batch calculate fees for multiple transactions
   * Optimized for high-volume processing
   */
  public async batchCalculateTransactionFees(
    transactions: Array<{
      partnershipId: string;
      attributedRevenue: number;
      customerTier: CustomerTier;
      monthlyVolume: number;
    }>
  ): Promise<TransactionFeeCalculation[]> {
    const startTime = performance.now();
    
    const calculations = await Promise.all(
      transactions.map(tx => 
        this.calculateTransactionFee(
          tx.partnershipId,
          tx.attributedRevenue,
          tx.customerTier,
          tx.monthlyVolume
        )
      )
    );

    const totalProcessingTime = performance.now() - startTime;
    const avgProcessingTime = totalProcessingTime / transactions.length;

    console.log(`Batch calculation completed: ${transactions.length} transactions in ${totalProcessingTime.toFixed(2)}ms (avg: ${avgProcessingTime.toFixed(2)}ms per transaction)`);

    return calculations;
  }
}

// Export singleton instance
export const transactionFeeCalculator = TransactionFeeCalculator.getInstance();
