#!/usr/bin/env -S deno run -A
// Revenue Infrastructure Validation Test Suite
// Comprehensive testing of transaction fees, attribution, and tier access control
// Target: <50ms transaction fees, <100ms attribution, <10ms access control

import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { transactionFeeCalculator } from "../utils/transactionFeeCalculator.ts";
import { RevenueAttributionEngine } from "../utils/revenueAttributionEngine.ts";
import { createTierAccessControl } from "../utils/tierAccessControl.ts";

interface ValidationResult {
  test: string;
  status: "PASS" | "FAIL" | "WARN";
  message: string;
  duration?: number;
  details?: any;
}

class RevenueInfrastructureValidator {
  private client: Client;
  private results: ValidationResult[] = [];
  private attributionEngine: RevenueAttributionEngine;
  private tierAccessControl: any;

  constructor() {
    this.client = new Client({
      user: "postgres",
      password: "password",
      database: "ecommerce_analytics",
      hostname: "localhost",
      port: 5432,
    });
  }

  async runValidation(): Promise<void> {
    console.log("🚀 Starting Revenue Infrastructure Validation");
    console.log("================================================================================");
    console.log(`📅 Test Run Started: ${new Date().toISOString()}`);
    console.log("🗄️ Database: ecommerce_analytics (live)");
    console.log("================================================================================\n");

    try {
      await this.client.connect();
      this.attributionEngine = RevenueAttributionEngine.getInstance(this.client);
      this.tierAccessControl = createTierAccessControl(this.client);
      
      // Core validation tests
      await this.validateDatabaseSchema();
      await this.validateTransactionFeeCalculator();
      await this.validateRevenueAttribution();
      await this.validateTierAccessControl();
      await this.validateAPIEndpoints();
      await this.validatePerformanceTargets();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Validation failed:", error);
    } finally {
      await this.client.end();
    }
  }

  private async validateDatabaseSchema(): Promise<void> {
    console.log("🗄️ PHASE 1: Database Schema Validation");
    
    const expectedTables = [
      'marketplace_transactions',
      'revenue_attributions',
      'marketplace_billing_events',
      'marketplace_volume_discounts',
      'marketplace_tier_features'
    ];

    for (const table of expectedTables) {
      const startTime = performance.now();
      try {
        const result = await this.client.queryArray(
          `SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1`,
          [table]
        );
        
        const exists = Number(result.rows[0][0]) === 1;
        const duration = performance.now() - startTime;
        
        this.results.push({
          test: `Revenue table ${table} exists`,
          status: exists ? "PASS" : "FAIL",
          message: exists ? `Table ${table} found` : `Table ${table} missing`,
          duration
        });

        if (exists) {
          // Check RLS is enabled
          const rlsResult = await this.client.queryArray(
            `SELECT rowsecurity FROM pg_tables WHERE tablename = $1`,
            [table]
          );
          
          const rlsEnabled = rlsResult.rows[0]?.[0] === true;
          
          this.results.push({
            test: `RLS enabled on ${table}`,
            status: rlsEnabled ? "PASS" : "WARN",
            message: rlsEnabled ? `RLS active on ${table}` : `RLS missing on ${table}`
          });
        }
      } catch (error) {
        this.results.push({
          test: `Table ${table} validation`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validateTransactionFeeCalculator(): Promise<void> {
    console.log("💰 PHASE 2: Transaction Fee Calculator Validation");
    
    const testCases = [
      {
        name: "Advanced tier basic calculation",
        partnershipId: "test-partnership-1",
        revenue: 1000,
        tier: "advanced" as const,
        monthlyVolume: 0,
        expectedFeePercentage: 5.0,
        expectedCommission: 50.0
      },
      {
        name: "Enterprise tier with volume discount",
        partnershipId: "test-partnership-2",
        revenue: 10000,
        tier: "enterprise" as const,
        monthlyVolume: 150000, // Qualifies for 10% discount
        expectedFeePercentage: 2.7, // 3% - 10% discount
        expectedCommission: 270.0
      },
      {
        name: "Strategic tier maximum discount",
        partnershipId: "test-partnership-3",
        revenue: 50000,
        tier: "strategic" as const,
        monthlyVolume: 1500000, // Qualifies for 30% discount
        expectedFeePercentage: 0.7, // 1% - 30% discount
        expectedCommission: 350.0
      }
    ];

    for (const testCase of testCases) {
      const startTime = performance.now();
      try {
        const calculation = await transactionFeeCalculator.calculateTransactionFee(
          testCase.partnershipId,
          testCase.revenue,
          testCase.tier,
          testCase.monthlyVolume
        );

        const duration = performance.now() - startTime;
        const performanceTargetMet = duration <= 50;

        // Validate calculation accuracy
        const feeAccurate = Math.abs(calculation.final_fee_percentage - testCase.expectedFeePercentage) < 0.01;
        const commissionAccurate = Math.abs(calculation.platform_commission - testCase.expectedCommission) < 0.01;
        const revenueBalanced = Math.abs((calculation.platform_commission + calculation.partner_payout) - testCase.revenue) < 0.01;

        this.results.push({
          test: `Transaction fee ${testCase.name}`,
          status: feeAccurate && commissionAccurate && revenueBalanced && performanceTargetMet ? "PASS" : "FAIL",
          message: `Fee: ${calculation.final_fee_percentage}% (expected: ${testCase.expectedFeePercentage}%), Commission: $${calculation.platform_commission} (expected: $${testCase.expectedCommission}), Performance: ${duration.toFixed(2)}ms`,
          duration,
          details: {
            calculation,
            expected: testCase,
            accuracy: { feeAccurate, commissionAccurate, revenueBalanced },
            performance_target_met: performanceTargetMet
          }
        });

      } catch (error) {
        this.results.push({
          test: `Transaction fee ${testCase.name}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validateRevenueAttribution(): Promise<void> {
    console.log("📊 PHASE 3: Revenue Attribution Engine Validation");
    
    // Test attribution models
    const attributionModels = ['last_touch', 'first_touch', 'linear', 'time_decay'] as const;
    
    for (const model of attributionModels) {
      const startTime = performance.now();
      try {
        const conversionEvent = {
          customer_id: `test-customer-${model}`,
          conversion_timestamp: new Date(),
          total_revenue: 1000,
          currency_code: 'USD',
          conversion_type: 'purchase',
          conversion_data: { test: true }
        };

        const attributionConfig = {
          attribution_window_hours: 168,
          attribution_model: model,
          time_decay_half_life_hours: 24
        };

        const results = await this.attributionEngine.processAttribution(conversionEvent, attributionConfig);
        const duration = performance.now() - startTime;
        const performanceTargetMet = duration <= 100;

        // Validate attribution logic
        let totalWeight = 0;
        let totalAttributedRevenue = 0;
        
        for (const result of results) {
          totalWeight += result.attribution_weight;
          totalAttributedRevenue += result.attributed_revenue;
        }

        const weightSumValid = Math.abs(totalWeight - 1.0) < 0.01 || results.length === 0;
        const revenueAttributionValid = Math.abs(totalAttributedRevenue - conversionEvent.total_revenue) < 0.01 || results.length === 0;

        this.results.push({
          test: `Attribution model ${model}`,
          status: weightSumValid && revenueAttributionValid && performanceTargetMet ? "PASS" : "WARN",
          message: `Touchpoints: ${results.length}, Weight sum: ${totalWeight.toFixed(4)}, Revenue: $${totalAttributedRevenue}, Performance: ${duration.toFixed(2)}ms`,
          duration,
          details: {
            model,
            touchpoint_count: results.length,
            total_weight: totalWeight,
            total_attributed_revenue: totalAttributedRevenue,
            weight_sum_valid: weightSumValid,
            revenue_attribution_valid: revenueAttributionValid,
            performance_target_met: performanceTargetMet
          }
        });

      } catch (error) {
        this.results.push({
          test: `Attribution model ${model}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validateTierAccessControl(): Promise<void> {
    console.log("🔒 PHASE 4: Tier Access Control Validation");
    
    const accessTests = [
      {
        name: "Core tier basic analytics access",
        tenantId: "test-tenant-core",
        tier: "core" as const,
        feature: "basic_analytics",
        expectedAccess: true
      },
      {
        name: "Core tier marketplace access (should fail)",
        tenantId: "test-tenant-core",
        tier: "core" as const,
        feature: "partner_discovery",
        expectedAccess: false
      },
      {
        name: "Advanced tier marketplace access",
        tenantId: "test-tenant-advanced",
        tier: "advanced" as const,
        feature: "partner_discovery",
        expectedAccess: true
      },
      {
        name: "Enterprise tier revenue sharing",
        tenantId: "test-tenant-enterprise",
        tier: "enterprise" as const,
        feature: "revenue_sharing",
        expectedAccess: true
      },
      {
        name: "Strategic tier white label",
        tenantId: "test-tenant-strategic",
        tier: "strategic" as const,
        feature: "white_label",
        expectedAccess: true
      }
    ];

    for (const test of accessTests) {
      const startTime = performance.now();
      try {
        const accessResult = await this.tierAccessControl.checkAccess(
          test.tenantId,
          test.tier,
          test.feature
        );

        const duration = performance.now() - startTime;
        const performanceTargetMet = duration <= 10;
        const accessCorrect = accessResult.allowed === test.expectedAccess;

        this.results.push({
          test: `Access control ${test.name}`,
          status: accessCorrect && performanceTargetMet ? "PASS" : "FAIL",
          message: `Access: ${accessResult.allowed} (expected: ${test.expectedAccess}), Performance: ${duration.toFixed(2)}ms, Reason: ${accessResult.reason || 'N/A'}`,
          duration,
          details: {
            access_result: accessResult,
            expected_access: test.expectedAccess,
            access_correct: accessCorrect,
            performance_target_met: performanceTargetMet
          }
        });

      } catch (error) {
        this.results.push({
          test: `Access control ${test.name}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validateAPIEndpoints(): Promise<void> {
    console.log("🔌 PHASE 5: API Endpoint Validation");
    
    const baseUrl = "http://localhost:8001";
    const endpoints = [
      {
        name: "Transaction fees calculation",
        method: "POST",
        path: "/api/revenue/transaction-fees",
        body: {
          partnership_id: "test-partnership-api",
          attributed_revenue: 5000,
          customer_tier: "enterprise",
          monthly_volume: 100000
        }
      }
    ];

    for (const endpoint of endpoints) {
      const startTime = performance.now();
      try {
        const response = await fetch(`${baseUrl}${endpoint.path}`, {
          method: endpoint.method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(endpoint.body)
        });

        const duration = performance.now() - startTime;
        const responseData = await response.json();

        this.results.push({
          test: `API ${endpoint.name}`,
          status: response.ok && responseData.success ? "PASS" : "WARN",
          message: `${response.status} - ${duration.toFixed(2)}ms - Success: ${responseData.success}`,
          duration,
          details: {
            status: response.status,
            response_data: responseData,
            endpoint: endpoint.path
          }
        });

      } catch (error) {
        this.results.push({
          test: `API ${endpoint.name}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validatePerformanceTargets(): Promise<void> {
    console.log("⚡ PHASE 6: Performance Target Validation");
    
    // Batch performance test
    const batchSize = 100;
    const transactions = Array.from({ length: batchSize }, (_, i) => ({
      partnershipId: `batch-test-${i}`,
      attributedRevenue: Math.random() * 10000 + 1000,
      customerTier: ['advanced', 'enterprise', 'strategic'][i % 3] as const,
      monthlyVolume: Math.random() * 500000
    }));

    const startTime = performance.now();
    try {
      const calculations = await transactionFeeCalculator.batchCalculateTransactionFees(transactions);
      const duration = performance.now() - startTime;
      const avgTimePerTransaction = duration / batchSize;
      const throughputPerSecond = (batchSize / duration) * 1000;

      this.results.push({
        test: "Batch transaction fee performance",
        status: avgTimePerTransaction <= 10 ? "PASS" : "WARN",
        message: `${batchSize} transactions in ${duration.toFixed(2)}ms (avg: ${avgTimePerTransaction.toFixed(2)}ms per transaction, throughput: ${throughputPerSecond.toFixed(0)}/sec)`,
        duration,
        details: {
          batch_size: batchSize,
          total_time: duration,
          avg_time_per_transaction: avgTimePerTransaction,
          throughput_per_second: throughputPerSecond,
          target_met: avgTimePerTransaction <= 10
        }
      });

    } catch (error) {
      this.results.push({
        test: "Batch transaction fee performance",
        status: "FAIL",
        message: `Error: ${error.message}`
      });
    }
  }

  private printResults(): void {
    console.log("\n================================================================================");
    console.log("📊 REVENUE INFRASTRUCTURE VALIDATION REPORT");
    console.log("================================================================================");
    
    const passed = this.results.filter(r => r.status === "PASS").length;
    const failed = this.results.filter(r => r.status === "FAIL").length;
    const warnings = this.results.filter(r => r.status === "WARN").length;
    const total = this.results.length;
    
    console.log(`📅 Completed: ${new Date().toISOString()}`);
    console.log(`📊 Results: ${passed}/${total} passed, ${failed} failed, ${warnings} warnings`);
    console.log(`🎯 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    // Performance summary
    const performanceTests = this.results.filter(r => r.duration !== undefined);
    if (performanceTests.length > 0) {
      const avgPerformance = performanceTests.reduce((sum, test) => sum + (test.duration || 0), 0) / performanceTests.length;
      console.log(`⚡ Average Response Time: ${avgPerformance.toFixed(2)}ms`);
    }
    
    console.log("\n📋 DETAILED RESULTS:");
    
    for (const result of this.results) {
      const icon = result.status === "PASS" ? "✅" : result.status === "WARN" ? "⚠️" : "❌";
      const duration = result.duration ? ` (${result.duration.toFixed(2)}ms)` : "";
      console.log(`${icon} ${result.test}: ${result.message}${duration}`);
    }
    
    console.log("\n================================================================================");
    
    if (failed === 0) {
      console.log("🎉 REVENUE INFRASTRUCTURE VALIDATION: ✅ PASSED");
      console.log("✅ Revenue infrastructure is ready for production deployment!");
    } else {
      console.log("⚠️ REVENUE INFRASTRUCTURE VALIDATION: ❌ ISSUES FOUND");
      console.log("🔧 Please address the failed tests before proceeding.");
    }
    
    console.log("================================================================================");
  }
}

// Run validation if this file is executed directly
if (import.meta.main) {
  const validator = new RevenueInfrastructureValidator();
  await validator.runValidation();
}

export { RevenueInfrastructureValidator };
