#!/usr/bin/env -S deno run -A
// Marketplace Live Integration Testing
// Comprehensive testing against running Fresh server and live database

interface TestResult {
  test: string;
  status: "PASS" | "FAIL" | "WARN";
  message: string;
  duration?: number;
  details?: any;
}

class MarketplaceLiveIntegrationTester {
  private baseUrl = "http://localhost:8001";
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log("🚀 Starting Marketplace Live Integration Tests");
    console.log("================================================================================");
    console.log(`📅 Test Run Started: ${new Date().toISOString()}`);
    console.log(`🌐 Server URL: ${this.baseUrl}`);
    console.log("🗄️ Database: ecommerce_analytics (live)");
    console.log("================================================================================\n");

    try {
      // Phase 1: Server Health & Route Validation
      await this.testServerHealth();
      await this.testMarketplaceRoutes();
      
      // Phase 2: API Endpoint Testing
      await this.testAPIEndpoints();
      
      // Phase 3: Frontend Component Testing
      await this.testFrontendComponents();
      
      // Phase 4: Performance Testing
      await this.testPerformance();
      
      // Phase 5: Security Testing
      await this.testSecurity();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Integration testing failed:", error);
    }
  }

  private async testServerHealth(): Promise<void> {
    console.log("🏥 PHASE 1: Server Health & Route Validation");
    
    // Test server is responding
    const startTime = performance.now();
    try {
      const response = await fetch(this.baseUrl);
      const duration = performance.now() - startTime;
      
      this.results.push({
        test: "Server health check",
        status: response.ok ? "PASS" : "FAIL",
        message: `Server responded with ${response.status} in ${duration.toFixed(2)}ms`,
        duration
      });
    } catch (error) {
      this.results.push({
        test: "Server health check",
        status: "FAIL",
        message: `Server unreachable: ${error.message}`
      });
    }
  }

  private async testMarketplaceRoutes(): Promise<void> {
    console.log("🗺️ Testing Marketplace Routes");
    
    const routes = [
      { path: "/marketplace", name: "Marketplace Portal" },
      { path: "/marketplace/discover", name: "Partner Discovery" },
      { path: "/marketplace/partnerships", name: "Partnership Management" },
      { path: "/marketplace/analytics", name: "Marketplace Analytics" }
    ];

    for (const route of routes) {
      const startTime = performance.now();
      try {
        const response = await fetch(`${this.baseUrl}${route.path}`);
        const duration = performance.now() - startTime;
        const text = await response.text();
        
        // Check for Fresh SSR indicators
        const hasSSR = text.includes('data-fresh-key') || text.includes('_fresh');
        const hasMarketplaceContent = text.includes('marketplace') || text.includes('partnership');
        
        this.results.push({
          test: `Route ${route.path}`,
          status: response.ok && hasSSR ? "PASS" : "WARN",
          message: `${response.status} - ${duration.toFixed(2)}ms - SSR: ${hasSSR} - Content: ${hasMarketplaceContent}`,
          duration,
          details: { 
            status: response.status, 
            hasSSR, 
            hasMarketplaceContent,
            contentLength: text.length 
          }
        });
      } catch (error) {
        this.results.push({
          test: `Route ${route.path}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async testAPIEndpoints(): Promise<void> {
    console.log("🔌 PHASE 2: API Endpoint Testing");
    
    const apiEndpoints = [
      { 
        path: "/api/marketplace/partnerships", 
        method: "GET",
        name: "Get Partnerships"
      },
      { 
        path: "/api/marketplace/partners/discover", 
        method: "GET",
        name: "Partner Discovery API"
      },
      { 
        path: "/api/marketplace/analytics/partnership-metrics", 
        method: "GET",
        name: "Partnership Metrics API"
      },
      { 
        path: "/api/marketplace/insights/benchmarks", 
        method: "GET",
        name: "Industry Benchmarks API"
      }
    ];

    for (const endpoint of apiEndpoints) {
      const startTime = performance.now();
      try {
        const response = await fetch(`${this.baseUrl}${endpoint.path}`, {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/json',
            // Note: In real testing, we'd include proper auth headers
          }
        });
        
        const duration = performance.now() - startTime;
        let responseData;
        
        try {
          responseData = await response.json();
        } catch {
          responseData = await response.text();
        }
        
        // Check for expected API response structure
        const hasExpectedStructure = typeof responseData === 'object' && 
          (responseData.success !== undefined || responseData.data !== undefined || responseData.error !== undefined);
        
        this.results.push({
          test: `API ${endpoint.name}`,
          status: response.ok && hasExpectedStructure ? "PASS" : response.status === 401 ? "WARN" : "FAIL",
          message: `${response.status} - ${duration.toFixed(2)}ms - Structure: ${hasExpectedStructure}`,
          duration,
          details: { 
            status: response.status,
            hasExpectedStructure,
            responseType: typeof responseData,
            dataKeys: typeof responseData === 'object' ? Object.keys(responseData) : []
          }
        });
      } catch (error) {
        this.results.push({
          test: `API ${endpoint.name}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async testFrontendComponents(): Promise<void> {
    console.log("🏝️ PHASE 3: Frontend Component Testing");
    
    // Test marketplace portal page for component structure
    try {
      const response = await fetch(`${this.baseUrl}/marketplace`);
      const html = await response.text();
      
      // Check for key marketplace components
      const componentChecks = [
        { name: "MarketplaceOverview", selector: 'marketplace-overview', present: html.includes('marketplace-overview') },
        { name: "OpportunityFeed", selector: 'opportunity-feed', present: html.includes('opportunity-feed') },
        { name: "PartnershipMetrics", selector: 'partnership-metrics', present: html.includes('partnership-metrics') },
        { name: "TailwindCSS", selector: 'tailwind', present: html.includes('tailwind') || html.includes('tw-') },
        { name: "DarkMode", selector: 'dark:', present: html.includes('dark:') },
        { name: "ResponsiveDesign", selector: 'responsive', present: html.includes('sm:') || html.includes('md:') || html.includes('lg:') }
      ];
      
      for (const check of componentChecks) {
        this.results.push({
          test: `Frontend ${check.name}`,
          status: check.present ? "PASS" : "WARN",
          message: check.present ? `${check.name} detected` : `${check.name} not found`,
          details: { selector: check.selector, present: check.present }
        });
      }
      
      // Check for Fresh Islands architecture
      const hasIslands = html.includes('data-fresh-key') || html.includes('_fresh');
      this.results.push({
        test: "Fresh Islands Architecture",
        status: hasIslands ? "PASS" : "FAIL",
        message: hasIslands ? "Fresh Islands detected" : "Fresh Islands not found",
        details: { hasIslands }
      });
      
    } catch (error) {
      this.results.push({
        test: "Frontend component analysis",
        status: "FAIL",
        message: `Error: ${error.message}`
      });
    }
  }

  private async testPerformance(): Promise<void> {
    console.log("⚡ PHASE 4: Performance Testing");
    
    const performanceTests = [
      { path: "/marketplace", name: "Marketplace Portal", target: 500 },
      { path: "/marketplace/discover", name: "Partner Discovery", target: 500 },
      { path: "/api/marketplace/partnerships", name: "Partnerships API", target: 100 },
      { path: "/api/marketplace/analytics/partnership-metrics", name: "Analytics API", target: 100 }
    ];

    for (const test of performanceTests) {
      const measurements = [];
      
      // Run 3 measurements for average
      for (let i = 0; i < 3; i++) {
        const startTime = performance.now();
        try {
          const response = await fetch(`${this.baseUrl}${test.path}`);
          await response.text(); // Ensure full response is received
          const duration = performance.now() - startTime;
          measurements.push(duration);
        } catch (error) {
          measurements.push(999999); // High penalty for errors
        }
      }
      
      const avgDuration = measurements.reduce((a, b) => a + b, 0) / measurements.length;
      const minDuration = Math.min(...measurements);
      const maxDuration = Math.max(...measurements);
      
      this.results.push({
        test: `Performance ${test.name}`,
        status: avgDuration <= test.target ? "PASS" : avgDuration <= test.target * 2 ? "WARN" : "FAIL",
        message: `Avg: ${avgDuration.toFixed(2)}ms (target: ${test.target}ms) - Range: ${minDuration.toFixed(2)}-${maxDuration.toFixed(2)}ms`,
        duration: avgDuration,
        details: { 
          target: test.target, 
          average: avgDuration, 
          min: minDuration, 
          max: maxDuration,
          measurements 
        }
      });
    }
  }

  private async testSecurity(): Promise<void> {
    console.log("🔒 PHASE 5: Security Testing");
    
    // Test for basic security headers and practices
    try {
      const response = await fetch(`${this.baseUrl}/marketplace`);
      const headers = response.headers;
      
      const securityChecks = [
        { 
          name: "Content-Type Header", 
          check: headers.get('content-type')?.includes('text/html'),
          message: headers.get('content-type') || 'Missing'
        },
        { 
          name: "X-Frame-Options", 
          check: headers.has('x-frame-options'),
          message: headers.get('x-frame-options') || 'Missing'
        },
        { 
          name: "Content-Security-Policy", 
          check: headers.has('content-security-policy'),
          message: headers.get('content-security-policy') ? 'Present' : 'Missing'
        }
      ];
      
      for (const check of securityChecks) {
        this.results.push({
          test: `Security ${check.name}`,
          status: check.check ? "PASS" : "WARN",
          message: check.message,
          details: { headerPresent: check.check }
        });
      }
      
    } catch (error) {
      this.results.push({
        test: "Security headers analysis",
        status: "FAIL",
        message: `Error: ${error.message}`
      });
    }
  }

  private printResults(): void {
    console.log("\n================================================================================");
    console.log("📊 MARKETPLACE LIVE INTEGRATION TEST REPORT");
    console.log("================================================================================");
    
    const passed = this.results.filter(r => r.status === "PASS").length;
    const failed = this.results.filter(r => r.status === "FAIL").length;
    const warnings = this.results.filter(r => r.status === "WARN").length;
    const total = this.results.length;
    
    console.log(`📅 Completed: ${new Date().toISOString()}`);
    console.log(`🌐 Server: ${this.baseUrl}`);
    console.log(`📊 Results: ${passed}/${total} passed, ${failed} failed, ${warnings} warnings`);
    console.log(`🎯 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    // Performance summary
    const performanceTests = this.results.filter(r => r.test.startsWith('Performance'));
    if (performanceTests.length > 0) {
      const avgPerformance = performanceTests.reduce((sum, test) => sum + (test.duration || 0), 0) / performanceTests.length;
      console.log(`⚡ Average Response Time: ${avgPerformance.toFixed(2)}ms`);
    }
    
    console.log("\n📋 DETAILED RESULTS:");
    
    let currentPhase = "";
    for (const result of this.results) {
      const phase = result.test.split(' ')[0];
      if (phase !== currentPhase) {
        currentPhase = phase;
        console.log(`\n--- ${phase.toUpperCase()} TESTS ---`);
      }
      
      const icon = result.status === "PASS" ? "✅" : result.status === "WARN" ? "⚠️" : "❌";
      const duration = result.duration ? ` (${result.duration.toFixed(2)}ms)` : "";
      console.log(`${icon} ${result.test}: ${result.message}${duration}`);
    }
    
    console.log("\n================================================================================");
    
    if (failed === 0) {
      console.log("🎉 MARKETPLACE LIVE INTEGRATION: ✅ PASSED");
      console.log("✅ Marketplace ecosystem is ready for beta testing!");
    } else {
      console.log("⚠️ MARKETPLACE LIVE INTEGRATION: ❌ ISSUES FOUND");
      console.log("🔧 Please address the failed tests before proceeding to beta testing.");
    }
    
    console.log("================================================================================");
  }
}

// Run integration tests if this file is executed directly
if (import.meta.main) {
  const tester = new MarketplaceLiveIntegrationTester();
  await tester.runAllTests();
}

export { MarketplaceLiveIntegrationTester };
