#!/usr/bin/env -S deno run -A
// Marketplace Production Validation Test
// Validates marketplace functionality against the production database with real data

import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";

interface ValidationResult {
  test: string;
  status: "PASS" | "FAIL" | "WARN";
  message: string;
  duration?: number;
  details?: any;
}

class MarketplaceProductionValidator {
  private client: Client;
  private results: ValidationResult[] = [];

  constructor() {
    this.client = new Client({
      user: "postgres",
      password: "password",
      database: "ecommerce_analytics",
      hostname: "localhost",
      port: 5432,
    });
  }

  async runValidation(): Promise<void> {
    console.log("🚀 Starting Marketplace Production Validation");
    console.log("================================================================================");
    console.log(`📅 Test Run Started: ${new Date().toISOString()}`);
    console.log("🗄️ Database: ecommerce_analytics (production)");
    console.log("================================================================================\n");

    try {
      await this.client.connect();
      
      // Core validation tests
      await this.validateDatabaseSchema();
      await this.validateTimescaleDBFeatures();
      await this.validateRLSPolicies();
      await this.validateContinuousAggregates();
      await this.validateDataIntegrity();
      await this.validatePerformance();
      
      this.printResults();
    } catch (error) {
      console.error("❌ Validation failed:", error);
    } finally {
      await this.client.end();
    }
  }

  private async validateDatabaseSchema(): Promise<void> {
    console.log("🔍 PHASE 1: Database Schema Validation");
    
    const expectedTables = [
      'marketplace_partnerships',
      'cross_business_events', 
      'partner_compatibility_scores',
      'marketplace_user_preferences',
      'network_insights_cache'
    ];

    for (const table of expectedTables) {
      const startTime = performance.now();
      try {
        const result = await this.client.queryArray(
          `SELECT COUNT(*) FROM information_schema.tables WHERE table_name = $1`,
          [table]
        );

        const exists = Number(result.rows[0][0]) === 1;
        const duration = performance.now() - startTime;
        
        this.results.push({
          test: `Table ${table} exists`,
          status: exists ? "PASS" : "FAIL",
          message: exists ? `Table ${table} found` : `Table ${table} missing`,
          duration
        });
        
        if (exists) {
          // Check row count
          const countResult = await this.client.queryArray(`SELECT COUNT(*) FROM ${table}`);
          const rowCount = countResult.rows[0][0];
          
          this.results.push({
            test: `Table ${table} data`,
            status: rowCount > 0 ? "PASS" : "WARN",
            message: `${rowCount} rows found`,
            details: { rowCount }
          });
        }
      } catch (error) {
        this.results.push({
          test: `Table ${table} validation`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validateTimescaleDBFeatures(): Promise<void> {
    console.log("⏰ PHASE 2: TimescaleDB Features Validation");
    
    try {
      // Check TimescaleDB extension
      const extResult = await this.client.queryArray(
        `SELECT COUNT(*) FROM pg_extension WHERE extname = 'timescaledb'`
      );

      this.results.push({
        test: "TimescaleDB extension",
        status: Number(extResult.rows[0][0]) === 1 ? "PASS" : "FAIL",
        message: Number(extResult.rows[0][0]) === 1 ? "TimescaleDB extension active" : "TimescaleDB extension missing"
      });

      // Check hypertables
      const hypertableResult = await this.client.queryArray(
        `SELECT hypertable_name, num_chunks FROM timescaledb_information.hypertables WHERE hypertable_name = 'cross_business_events'`
      );
      
      this.results.push({
        test: "cross_business_events hypertable",
        status: hypertableResult.rows.length > 0 ? "PASS" : "FAIL",
        message: hypertableResult.rows.length > 0 
          ? `Hypertable active with ${hypertableResult.rows[0][1]} chunks`
          : "Hypertable not found",
        details: hypertableResult.rows[0]
      });

    } catch (error) {
      this.results.push({
        test: "TimescaleDB validation",
        status: "FAIL",
        message: `Error: ${error.message}`
      });
    }
  }

  private async validateRLSPolicies(): Promise<void> {
    console.log("🔒 PHASE 3: Row Level Security Validation");
    
    const rlsTables = [
      'marketplace_partnerships',
      'cross_business_events',
      'partner_compatibility_scores',
      'marketplace_user_preferences',
      'network_insights_cache'
    ];

    for (const table of rlsTables) {
      try {
        const result = await this.client.queryArray(
          `SELECT rowsecurity FROM pg_tables WHERE tablename = $1`,
          [table]
        );
        
        const rlsEnabled = result.rows[0]?.[0] === true;
        
        this.results.push({
          test: `RLS enabled on ${table}`,
          status: rlsEnabled ? "PASS" : "FAIL",
          message: rlsEnabled ? `RLS active on ${table}` : `RLS missing on ${table}`
        });
      } catch (error) {
        this.results.push({
          test: `RLS validation for ${table}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validateContinuousAggregates(): Promise<void> {
    console.log("📊 PHASE 4: Continuous Aggregates Validation");
    
    const expectedViews = [
      'marketplace_partnership_metrics',
      'marketplace_network_trends', 
      'tenant_marketplace_activity',
      'realtime_partnership_performance'
    ];

    for (const view of expectedViews) {
      try {
        const result = await this.client.queryArray(
          `SELECT view_name FROM timescaledb_information.continuous_aggregates WHERE view_name = $1`,
          [view]
        );
        
        const exists = result.rows.length > 0;
        
        this.results.push({
          test: `Continuous aggregate ${view}`,
          status: exists ? "PASS" : "FAIL",
          message: exists ? `View ${view} active` : `View ${view} missing`
        });

        if (exists) {
          // Test data access
          const dataResult = await this.client.queryArray(`SELECT COUNT(*) FROM ${view}`);
          const rowCount = dataResult.rows[0][0];
          
          this.results.push({
            test: `${view} data access`,
            status: "PASS",
            message: `${rowCount} aggregated rows`,
            details: { rowCount }
          });
        }
      } catch (error) {
        this.results.push({
          test: `Continuous aggregate ${view}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private async validateDataIntegrity(): Promise<void> {
    console.log("✅ PHASE 5: Data Integrity Validation");
    
    try {
      // Check for orphaned records
      const orphanCheck = await this.client.queryArray(`
        SELECT COUNT(*) FROM cross_business_events cbe
        LEFT JOIN marketplace_partnerships mp ON cbe.partnership_id = mp.id
        WHERE mp.id IS NULL AND cbe.partnership_id IS NOT NULL
      `);
      
      const orphanCount = orphanCheck.rows[0][0];
      
      this.results.push({
        test: "Data integrity - orphaned events",
        status: orphanCount === 0 ? "PASS" : "WARN",
        message: orphanCount === 0 ? "No orphaned events found" : `${orphanCount} orphaned events found`,
        details: { orphanCount }
      });

      // Check timestamp consistency
      const timestampCheck = await this.client.queryArray(`
        SELECT COUNT(*) FROM cross_business_events 
        WHERE time > NOW() OR time < '2020-01-01'
      `);
      
      const invalidTimestamps = timestampCheck.rows[0][0];
      
      this.results.push({
        test: "Data integrity - timestamp validity",
        status: invalidTimestamps === 0 ? "PASS" : "WARN",
        message: invalidTimestamps === 0 ? "All timestamps valid" : `${invalidTimestamps} invalid timestamps`,
        details: { invalidTimestamps }
      });

    } catch (error) {
      this.results.push({
        test: "Data integrity validation",
        status: "FAIL",
        message: `Error: ${error.message}`
      });
    }
  }

  private async validatePerformance(): Promise<void> {
    console.log("⚡ PHASE 6: Performance Validation");
    
    const performanceTests = [
      {
        name: "Basic partnership query",
        query: "SELECT * FROM marketplace_partnerships LIMIT 10",
        target: 10 // 10ms target
      },
      {
        name: "Cross-business events aggregation",
        query: `SELECT DATE_TRUNC('hour', time) as hour, COUNT(*) 
                FROM cross_business_events 
                WHERE time >= NOW() - INTERVAL '24 hours'
                GROUP BY hour ORDER BY hour DESC LIMIT 24`,
        target: 50 // 50ms target
      },
      {
        name: "Continuous aggregate query",
        query: "SELECT * FROM marketplace_partnership_metrics ORDER BY hour DESC LIMIT 20",
        target: 100 // 100ms target
      }
    ];

    for (const test of performanceTests) {
      const startTime = performance.now();
      try {
        await this.client.queryArray(test.query);
        const duration = performance.now() - startTime;
        
        this.results.push({
          test: `Performance - ${test.name}`,
          status: duration <= test.target ? "PASS" : "WARN",
          message: `${duration.toFixed(2)}ms (target: ${test.target}ms)`,
          duration,
          details: { target: test.target, actual: duration }
        });
      } catch (error) {
        this.results.push({
          test: `Performance - ${test.name}`,
          status: "FAIL",
          message: `Error: ${error.message}`
        });
      }
    }
  }

  private printResults(): void {
    console.log("\n================================================================================");
    console.log("📊 MARKETPLACE PRODUCTION VALIDATION REPORT");
    console.log("================================================================================");
    
    const passed = this.results.filter(r => r.status === "PASS").length;
    const failed = this.results.filter(r => r.status === "FAIL").length;
    const warnings = this.results.filter(r => r.status === "WARN").length;
    const total = this.results.length;
    
    console.log(`📅 Completed: ${new Date().toISOString()}`);
    console.log(`📊 Results: ${passed}/${total} passed, ${failed} failed, ${warnings} warnings`);
    console.log(`🎯 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    console.log("\n📋 DETAILED RESULTS:");
    
    for (const result of this.results) {
      const icon = result.status === "PASS" ? "✅" : result.status === "WARN" ? "⚠️" : "❌";
      const duration = result.duration ? ` (${result.duration.toFixed(2)}ms)` : "";
      console.log(`${icon} ${result.test}: ${result.message}${duration}`);
    }
    
    console.log("\n================================================================================");
    
    if (failed === 0) {
      console.log("🎉 MARKETPLACE VALIDATION: ✅ PASSED");
      console.log("✅ Marketplace ecosystem is ready for production deployment!");
    } else {
      console.log("⚠️ MARKETPLACE VALIDATION: ❌ ISSUES FOUND");
      console.log("🔧 Please address the failed tests before proceeding.");
    }
    
    console.log("================================================================================");
  }
}

// Run validation if this file is executed directly
if (import.meta.main) {
  const validator = new MarketplaceProductionValidator();
  await validator.runValidation();
}

export { MarketplaceProductionValidator };
