// Data Products Marketplace API Endpoints
// RESTful API for data product catalog, subscriptions, and revenue management
// Target: $175K ARR from data products revenue stream

import { Handlers } from "$fresh/server.ts";
import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { DataProductsMarketplace } from "../../../../utils/dataProductsMarketplace.ts";

interface DataProductRequest {
  product_name: string;
  product_description: string;
  product_category: 'industry_benchmarks' | 'trend_analysis' | 'competitive_intelligence' | 'custom_analytics';
  creator_tier: 'enterprise' | 'strategic';
  base_price_monthly: number;
  pricing_model?: 'subscription' | 'one_time' | 'usage_based';
  data_format?: 'json' | 'csv' | 'parquet' | 'api';
  update_frequency?: 'real_time' | 'hourly' | 'daily' | 'weekly' | 'monthly';
  data_retention_days?: number;
  data_quality_score?: number;
  sample_size_avg?: number;
  accuracy_percentage?: number;
  creator_rating?: number;
  product_metadata?: Record<string, unknown>;
  tags?: string[];
  is_featured?: boolean;
}

interface MarketplaceResponse {
  success: boolean;
  data?: unknown;
  error?: string;
  processing_time_ms?: number;
  metadata?: {
    total_count?: number;
    page_size?: number;
    current_page?: number;
    performance_target_met?: boolean;
  };
}

// Database connection factory
function createClient(): Client {
  return new Client({
    user: "postgres",
    password: "password",
    database: "ecommerce_analytics",
    hostname: "localhost",
    port: 5432,
  });
}

export const handler: Handlers = {
  /**
   * GET /api/marketplace/data-products
   * Get marketplace catalog with filtering and pagination
   */
  async GET(req, _ctx) {
    const startTime = performance.now();
    const client = createClient();
    
    try {
      await client.connect();
      
      const marketplace = DataProductsMarketplace.getInstance(client);
      
      const url = new URL(req.url);
      
      // Parse filters
      const filters = {
        category: url.searchParams.get("category") || undefined,
        minPrice: url.searchParams.get("min_price") ? parseFloat(url.searchParams.get("min_price")!) : undefined,
        maxPrice: url.searchParams.get("max_price") ? parseFloat(url.searchParams.get("max_price")!) : undefined,
        creatorTier: url.searchParams.get("creator_tier") || undefined,
        featured: url.searchParams.get("featured") ? url.searchParams.get("featured") === "true" : undefined,
        tags: url.searchParams.get("tags") ? url.searchParams.get("tags")!.split(",") : undefined
      };

      // Parse pagination
      const pagination = {
        limit: url.searchParams.get("limit") ? parseInt(url.searchParams.get("limit")!) : 20,
        offset: url.searchParams.get("offset") ? parseInt(url.searchParams.get("offset")!) : 0
      };

      const result = await marketplace.getMarketplaceCatalog(filters, pagination);

      const processingTime = performance.now() - startTime;
      const performanceTargetMet = processingTime <= 100;

      const response: MarketplaceResponse = {
        success: true,
        data: result.products,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          total_count: result.total,
          page_size: pagination.limit,
          current_page: Math.floor(pagination.offset / pagination.limit) + 1,
          performance_target_met: performanceTargetMet
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Data products catalog retrieval failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  },

  /**
   * POST /api/marketplace/data-products
   * Create a new data product
   */
  async POST(req, _ctx) {
    const startTime = performance.now();
    const client = createClient();
    
    try {
      await client.connect();
      
      const marketplace = DataProductsMarketplace.getInstance(client);
      
      const body: DataProductRequest = await req.json();
      
      // Validate required fields
      if (!body.product_name || !body.product_description || !body.product_category || 
          !body.creator_tier || body.base_price_monthly === undefined) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required fields: product_name, product_description, product_category, creator_tier, base_price_monthly"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // For now, use a placeholder creator tenant ID - in production this would come from authentication
      const creatorTenantId = crypto.randomUUID();

      const productData = {
        product_name: body.product_name,
        product_description: body.product_description,
        product_category: body.product_category,
        creator_tier: body.creator_tier,
        base_price_monthly: body.base_price_monthly,
        pricing_model: body.pricing_model || 'subscription',
        is_active: true,
        is_featured: body.is_featured || false,
        data_format: body.data_format || 'json',
        update_frequency: body.update_frequency || 'daily',
        data_retention_days: body.data_retention_days || 365,
        data_quality_score: body.data_quality_score,
        sample_size_avg: body.sample_size_avg,
        accuracy_percentage: body.accuracy_percentage || 95.0,
        creator_rating: body.creator_rating || 4.0,
        product_metadata: body.product_metadata,
        tags: body.tags
      };

      const product = await marketplace.createDataProduct(creatorTenantId, productData);

      const processingTime = performance.now() - startTime;
      const performanceTargetMet = processingTime <= 100;

      const response: MarketplaceResponse = {
        success: true,
        data: product,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          performance_target_met: performanceTargetMet
        }
      };

      return new Response(JSON.stringify(response), {
        status: 201,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Data product creation failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};
