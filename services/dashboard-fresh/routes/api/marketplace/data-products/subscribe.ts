// Data Product Subscription API Endpoint
// Subscribe to data products with automated revenue sharing
// Target: $175K ARR from data products revenue stream

import { Handlers } from "$fresh/server.ts";
import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { DataProductsMarketplace } from "../../../../utils/dataProductsMarketplace.ts";

interface SubscriptionRequest {
  product_id: string;
  subscriber_tier: 'advanced' | 'enterprise' | 'strategic';
  subscription_tier: 'basic' | 'premium' | 'enterprise';
  billing_cycle: 'monthly' | 'annual';
  payment_method: string;
  auto_renewal: boolean;
  monthly_price: number;
}

interface SubscriptionResponse {
  success: boolean;
  data?: unknown;
  error?: string;
  processing_time_ms?: number;
  metadata?: {
    performance_target_met?: boolean;
  };
}

// Database connection factory
function createClient(): Client {
  return new Client({
    user: "postgres",
    password: "password",
    database: "ecommerce_analytics",
    hostname: "localhost",
    port: 5432,
  });
}

const handler: Handlers = {
  /**
   * POST /api/marketplace/data-products/subscribe
   * Subscribe to a data product
   */
  async POST(req, _ctx) {
    const startTime = performance.now();
    const client = createClient();
    
    try {
      await client.connect();
      
      const marketplace = DataProductsMarketplace.getInstance(client);
      
      const body: SubscriptionRequest = await req.json();
      
      // Validate required fields
      if (!body.product_id || !body.subscriber_tier || !body.subscription_tier ||
          !body.billing_cycle || body.monthly_price === undefined) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required fields: product_id, subscriber_tier, subscription_tier, billing_cycle, monthly_price"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // For now, use a placeholder subscriber tenant ID - in production this would come from authentication
      const subscriberTenantId = crypto.randomUUID();

      const subscription = await marketplace.subscribeToProduct(
        body.product_id,
        subscriberTenantId,
        body.subscriber_tier,
        body.billing_cycle || 'monthly'
      );

      const processingTime = performance.now() - startTime;
      const performanceTargetMet = processingTime <= 100;

      const response: SubscriptionResponse = {
        success: true,
        data: subscription,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          performance_target_met: performanceTargetMet
        }
      };

      return new Response(JSON.stringify(response), {
        status: 201,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Data product subscription failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};

export default handler;
