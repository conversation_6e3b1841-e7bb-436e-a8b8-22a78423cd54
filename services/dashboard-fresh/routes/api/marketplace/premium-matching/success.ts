// Premium Matching Success Tracking API Endpoint
// Process success metrics and calculate bonuses
// Target: $160K ARR from premium matching revenue stream

import { Handlers } from "$fresh/server.ts";
import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { DataProductsMarketplace } from "../../../../utils/dataProductsMarketplace.ts";

interface SuccessMetricsRequest {
  matching_id: string;
  matched_partners_count: number;
  introduction_success_rate: number;
  customer_satisfaction_score: number;
  response_time_hours: number;
}

interface SuccessResponse {
  success: boolean;
  data?: unknown;
  error?: string;
  processing_time_ms?: number;
  metadata?: {
    performance_target_met?: boolean;
  };
}

// Database connection factory
function createClient(): Client {
  return new Client({
    user: "postgres",
    password: "password",
    database: "ecommerce_analytics",
    hostname: "localhost",
    port: 5432,
  });
}

const handler: Handlers = {
  /**
   * PUT /api/marketplace/premium-matching/success
   * Process premium matching success and calculate bonuses
   */
  async PUT(req, _ctx) {
    const startTime = performance.now();
    const client = createClient();
    
    try {
      await client.connect();
      
      const marketplace = DataProductsMarketplace.getInstance(client);
      
      const body: SuccessMetricsRequest = await req.json();
      
      // Validate required fields
      if (!body.matching_id || body.matched_partners_count === undefined || 
          body.introduction_success_rate === undefined || body.customer_satisfaction_score === undefined ||
          body.response_time_hours === undefined) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required fields: matching_id, matched_partners_count, introduction_success_rate, customer_satisfaction_score, response_time_hours"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      const successResult = await marketplace.processPremiumMatchingSuccess(
        body.matching_id,
        {
          matched_partners_count: body.matched_partners_count,
          introduction_success_rate: body.introduction_success_rate,
          customer_satisfaction_score: body.customer_satisfaction_score,
          response_time_hours: body.response_time_hours
        }
      );

      const processingTime = performance.now() - startTime;
      const performanceTargetMet = processingTime <= 100;

      const response: SuccessResponse = {
        success: true,
        data: {
          matching_id: body.matching_id,
          success_bonus: successResult.success_bonus,
          total_revenue: successResult.total_revenue,
          success_achieved: successResult.success_bonus > 0,
          performance_metrics: {
            matched_partners_count: body.matched_partners_count,
            introduction_success_rate: body.introduction_success_rate,
            customer_satisfaction_score: body.customer_satisfaction_score,
            response_time_hours: body.response_time_hours
          }
        },
        processing_time_ms: Math.round(processingTime),
        metadata: {
          performance_target_met: performanceTargetMet
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Premium matching success processing failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};

export default handler;
