// Revenue Attribution API Endpoints
// Real-time revenue attribution processing and analytics
// Target: <100ms attribution calculation, 99.9% accuracy

import { Handlers } from "$fresh/server.ts";
import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { RevenueAttributionEngine, ConversionEvent, AttributionConfig, AttributionModel } from "../../../utils/revenueAttributionEngine.ts";

interface AttributionRequest {
  conversion_event: {
    customer_id: string;
    session_id?: string;
    conversion_timestamp: string;
    total_revenue: number;
    currency_code?: string;
    conversion_type: string;
    conversion_data?: Record<string, any>;
  };
  attribution_config?: {
    attribution_window_hours?: number;
    attribution_model?: AttributionModel;
    time_decay_half_life_hours?: number;
    minimum_attribution_weight?: number;
    session_timeout_minutes?: number;
  };
}

interface AttributionSummaryRequest {
  partnership_id: string;
  start_date: string;
  end_date: string;
  attribution_model?: AttributionModel;
}

interface AttributionResponse {
  success: boolean;
  data?: any;
  error?: string;
  processing_time_ms?: number;
  metadata?: {
    total_attributions?: number;
    total_attributed_revenue?: number;
    attribution_model?: AttributionModel;
    performance_target_met?: boolean;
  };
}

// Database connection
const client = new Client({
  user: "postgres",
  password: "password",
  database: "ecommerce_analytics",
  hostname: "localhost",
  port: 5432,
});

let attributionEngine: RevenueAttributionEngine;

export const handler: Handlers = {
  /**
   * POST /api/revenue/attribution
   * Process revenue attribution for a conversion event
   */
  async POST(req, _ctx) {
    const startTime = performance.now();
    
    try {
      await client.connect();
      
      if (!attributionEngine) {
        attributionEngine = RevenueAttributionEngine.getInstance(client);
      }
      
      const body: AttributionRequest = await req.json();
      
      // Validate required fields
      if (!body.conversion_event || !body.conversion_event.customer_id || !body.conversion_event.conversion_timestamp || body.conversion_event.total_revenue === undefined) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required fields: customer_id, conversion_timestamp, total_revenue"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Prepare conversion event
      const conversionEvent: ConversionEvent = {
        customer_id: body.conversion_event.customer_id,
        session_id: body.conversion_event.session_id,
        conversion_timestamp: new Date(body.conversion_event.conversion_timestamp),
        total_revenue: body.conversion_event.total_revenue,
        currency_code: body.conversion_event.currency_code || 'USD',
        conversion_type: body.conversion_event.conversion_type,
        conversion_data: body.conversion_event.conversion_data || {}
      };

      // Prepare attribution config
      const attributionConfig: AttributionConfig = {
        attribution_window_hours: body.attribution_config?.attribution_window_hours || 168, // 7 days default
        attribution_model: body.attribution_config?.attribution_model || 'last_touch',
        time_decay_half_life_hours: body.attribution_config?.time_decay_half_life_hours || 24,
        minimum_attribution_weight: body.attribution_config?.minimum_attribution_weight || 0.01,
        session_timeout_minutes: body.attribution_config?.session_timeout_minutes || 30
      };

      // Process attribution
      const attributionResults = await attributionEngine.processAttribution(conversionEvent, attributionConfig);

      const processingTime = performance.now() - startTime;
      const performanceTargetMet = processingTime <= 100;

      // Calculate summary metrics
      const totalAttributedRevenue = attributionResults.reduce((sum, result) => sum + result.attributed_revenue, 0);

      const response: AttributionResponse = {
        success: true,
        data: attributionResults,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          total_attributions: attributionResults.length,
          total_attributed_revenue: Math.round(totalAttributedRevenue * 100) / 100,
          attribution_model: attributionConfig.attribution_model,
          performance_target_met: performanceTargetMet
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Revenue attribution processing failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  },

  /**
   * GET /api/revenue/attribution?partnership_id=xxx&start_date=xxx&end_date=xxx
   * Get attribution summary for a partnership
   */
  async GET(req, _ctx) {
    const startTime = performance.now();
    
    try {
      await client.connect();
      
      if (!attributionEngine) {
        attributionEngine = RevenueAttributionEngine.getInstance(client);
      }
      
      const url = new URL(req.url);
      const partnershipId = url.searchParams.get("partnership_id");
      const startDate = url.searchParams.get("start_date");
      const endDate = url.searchParams.get("end_date");
      const attributionModel = url.searchParams.get("attribution_model") as AttributionModel;

      if (!partnershipId || !startDate || !endDate) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required parameters: partnership_id, start_date, end_date"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Get attribution summary
      const summary = await attributionEngine.getAttributionSummary(
        partnershipId,
        new Date(startDate),
        new Date(endDate)
      );

      // Get detailed attribution data if requested
      const includeDetails = url.searchParams.get("include_details") === "true";
      let detailedData = null;

      if (includeDetails) {
        let query = `
          SELECT 
            id as attribution_id,
            event_id,
            customer_id,
            session_id,
            touchpoint_sequence,
            attribution_weight,
            attribution_model,
            total_revenue,
            attributed_revenue,
            currency_code,
            event_timestamp,
            conversion_timestamp,
            attribution_window_hours,
            processing_time_ms,
            attribution_metadata
          FROM revenue_attributions
          WHERE partnership_id = $1
            AND conversion_timestamp >= $2
            AND conversion_timestamp <= $3
        `;

        const params = [partnershipId, startDate, endDate];

        if (attributionModel) {
          query += ` AND attribution_model = $4`;
          params.push(attributionModel);
        }

        query += ` ORDER BY conversion_timestamp DESC, touchpoint_sequence ASC`;

        const result = await client.queryObject(query, params);
        detailedData = result.rows;
      }

      const processingTime = performance.now() - startTime;

      const response: AttributionResponse = {
        success: true,
        data: {
          summary,
          details: detailedData
        },
        processing_time_ms: Math.round(processingTime),
        metadata: {
          total_attributions: summary.total_conversions,
          total_attributed_revenue: summary.total_attributed_revenue,
          performance_target_met: processingTime <= 100
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Attribution summary retrieval failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};

/**
 * PUT /api/revenue/attribution/batch
 * Process multiple conversion events in batch
 */
export const batchHandler: Handlers = {
  async PUT(req, _ctx) {
    const startTime = performance.now();
    
    try {
      await client.connect();
      
      if (!attributionEngine) {
        attributionEngine = RevenueAttributionEngine.getInstance(client);
      }
      
      const body: {
        conversion_events: Array<{
          customer_id: string;
          session_id?: string;
          conversion_timestamp: string;
          total_revenue: number;
          currency_code?: string;
          conversion_type: string;
          conversion_data?: Record<string, any>;
        }>;
        attribution_config?: AttributionConfig;
      } = await req.json();
      
      if (!body.conversion_events || !Array.isArray(body.conversion_events) || body.conversion_events.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing or empty conversion_events array"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Limit batch size
      if (body.conversion_events.length > 500) {
        return new Response(JSON.stringify({
          success: false,
          error: "Batch size limited to 500 conversion events"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Prepare conversion events
      const conversionEvents: ConversionEvent[] = body.conversion_events.map(event => ({
        customer_id: event.customer_id,
        session_id: event.session_id,
        conversion_timestamp: new Date(event.conversion_timestamp),
        total_revenue: event.total_revenue,
        currency_code: event.currency_code || 'USD',
        conversion_type: event.conversion_type,
        conversion_data: event.conversion_data || {}
      }));

      // Prepare attribution config
      const attributionConfig: AttributionConfig = {
        attribution_window_hours: body.attribution_config?.attribution_window_hours || 168,
        attribution_model: body.attribution_config?.attribution_model || 'last_touch',
        time_decay_half_life_hours: body.attribution_config?.time_decay_half_life_hours || 24,
        minimum_attribution_weight: body.attribution_config?.minimum_attribution_weight || 0.01,
        session_timeout_minutes: body.attribution_config?.session_timeout_minutes || 30
      };

      // Process batch attribution
      const allAttributionResults = await attributionEngine.batchProcessAttribution(conversionEvents, attributionConfig);

      const processingTime = performance.now() - startTime;
      const averageProcessingTime = processingTime / body.conversion_events.length;
      const performanceTargetMet = averageProcessingTime <= 100;

      // Calculate summary metrics
      const totalAttributedRevenue = allAttributionResults.reduce((sum, result) => sum + result.attributed_revenue, 0);

      const response: AttributionResponse = {
        success: true,
        data: allAttributionResults,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          total_attributions: allAttributionResults.length,
          total_attributed_revenue: Math.round(totalAttributedRevenue * 100) / 100,
          attribution_model: attributionConfig.attribution_model,
          performance_target_met: performanceTargetMet,
          average_processing_time_per_event: Math.round(averageProcessingTime * 100) / 100,
          throughput_per_second: Math.round((body.conversion_events.length / processingTime) * 1000)
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Batch attribution processing failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};
