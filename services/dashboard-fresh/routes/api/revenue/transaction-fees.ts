// Transaction Fee API Endpoints
// RESTful API for marketplace transaction fee calculations and management
// Target: <50ms response time, 99.9% accuracy

import { Handlers } from "$fresh/server.ts";
import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { transactionFeeCalculator, TransactionFeeCalculation, CustomerTier } from "../../../utils/transactionFeeCalculator.ts";

interface TransactionFeeRequest {
  partnership_id: string;
  attributed_revenue: number;
  customer_tier: CustomerTier;
  monthly_volume?: number;
  attribution_model?: 'last_touch' | 'first_touch' | 'linear' | 'time_decay';
  attribution_confidence?: number;
  currency_code?: string;
}

interface BatchTransactionFeeRequest {
  transactions: Array<{
    partnership_id: string;
    attributed_revenue: number;
    customer_tier: CustomerTier;
    monthly_volume?: number;
  }>;
}

interface TransactionFeeResponse {
  success: boolean;
  data?: TransactionFeeCalculation | TransactionFeeCalculation[];
  error?: string;
  processing_time_ms?: number;
  metadata?: {
    total_transactions?: number;
    average_processing_time?: number;
    performance_target_met?: boolean;
  };
}

// Database connection
const client = new Client({
  user: "postgres",
  password: "password",
  database: "ecommerce_analytics",
  hostname: "localhost",
  port: 5432,
});

export const handler: Handlers = {
  /**
   * POST /api/revenue/transaction-fees
   * Calculate transaction fee for a single transaction
   */
  async POST(req, _ctx) {
    const startTime = performance.now();
    
    try {
      await client.connect();
      
      const body: TransactionFeeRequest = await req.json();
      
      // Validate required fields
      if (!body.partnership_id || body.attributed_revenue === undefined || !body.customer_tier) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required fields: partnership_id, attributed_revenue, customer_tier"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Calculate transaction fee
      const calculation = await transactionFeeCalculator.calculateTransactionFee(
        body.partnership_id,
        body.attributed_revenue,
        body.customer_tier,
        body.monthly_volume || 0,
        body.attribution_model || 'last_touch',
        body.attribution_confidence || 100,
        body.currency_code || 'USD'
      );

      // Store transaction in database
      await storeTransactionCalculation(client, calculation);

      const processingTime = performance.now() - startTime;
      const performanceTargetMet = processingTime <= 50;

      const response: TransactionFeeResponse = {
        success: true,
        data: calculation,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          performance_target_met: performanceTargetMet
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Transaction fee calculation failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  },

  /**
   * GET /api/revenue/transaction-fees?partnership_id=xxx&start_date=xxx&end_date=xxx
   * Get transaction fee history for a partnership
   */
  async GET(req, _ctx) {
    const startTime = performance.now();
    
    try {
      await client.connect();
      
      const url = new URL(req.url);
      const partnershipId = url.searchParams.get("partnership_id");
      const startDate = url.searchParams.get("start_date");
      const endDate = url.searchParams.get("end_date");
      const limit = parseInt(url.searchParams.get("limit") || "100");
      const offset = parseInt(url.searchParams.get("offset") || "0");

      if (!partnershipId) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing required parameter: partnership_id"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Build query with optional date filters
      let query = `
        SELECT 
          id,
          partnership_id,
          attributed_revenue,
          currency_code,
          customer_tier,
          base_fee_percentage,
          volume_discount_percentage,
          final_fee_percentage,
          platform_commission,
          partner_payout,
          attribution_model,
          attribution_confidence,
          status,
          processing_time_ms,
          created_at,
          calculated_at,
          billed_at,
          paid_at
        FROM marketplace_transactions
        WHERE partnership_id = $1
      `;
      
      const params: any[] = [partnershipId];
      let paramIndex = 2;

      if (startDate) {
        query += ` AND created_at >= $${paramIndex}`;
        params.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        query += ` AND created_at <= $${paramIndex}`;
        params.push(endDate);
        paramIndex++;
      }

      query += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit, offset);

      const result = await client.queryObject(query, params);

      // Get total count for pagination
      let countQuery = `
        SELECT COUNT(*) as total
        FROM marketplace_transactions
        WHERE partnership_id = $1
      `;
      const countParams = [partnershipId];
      let countParamIndex = 2;

      if (startDate) {
        countQuery += ` AND created_at >= $${countParamIndex}`;
        countParams.push(startDate);
        countParamIndex++;
      }

      if (endDate) {
        countQuery += ` AND created_at <= $${countParamIndex}`;
        countParams.push(endDate);
      }

      const countResult = await client.queryObject(countQuery, countParams);
      const totalCount = Number(countResult.rows[0].total);

      const processingTime = performance.now() - startTime;

      const response: TransactionFeeResponse = {
        success: true,
        data: result.rows,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          total_transactions: totalCount,
          returned_transactions: result.rows.length,
          limit,
          offset,
          performance_target_met: processingTime <= 100
        }
      };

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Transaction fee history retrieval failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};

/**
 * Store transaction calculation in database
 */
async function storeTransactionCalculation(
  client: Client, 
  calculation: TransactionFeeCalculation
): Promise<void> {
  const query = `
    INSERT INTO marketplace_transactions (
      partnership_id,
      source_tenant_id,
      target_tenant_id,
      attributed_revenue,
      currency_code,
      customer_tier,
      base_fee_percentage,
      volume_discount_percentage,
      final_fee_percentage,
      platform_commission,
      partner_payout,
      attribution_model,
      attribution_confidence,
      status,
      processing_time_ms,
      calculated_at,
      transaction_metadata
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
    )
  `;

  // For now, use placeholder tenant IDs - in production these would come from the partnership
  const sourceTenantId = crypto.randomUUID();
  const targetTenantId = crypto.randomUUID();

  await client.queryArray(query, [
    calculation.partnership_id,
    sourceTenantId,
    targetTenantId,
    calculation.attributed_revenue,
    calculation.currency_code,
    calculation.customer_tier,
    calculation.base_fee_percentage,
    calculation.volume_discount_percentage,
    calculation.final_fee_percentage,
    calculation.platform_commission,
    calculation.partner_payout,
    calculation.attribution_model,
    calculation.attribution_confidence,
    'calculated',
    calculation.processing_time_ms,
    calculation.calculation_timestamp.toISOString(),
    JSON.stringify({
      calculation_timestamp: calculation.calculation_timestamp.toISOString(),
      performance_metrics: {
        processing_time_ms: calculation.processing_time_ms,
        target_met: calculation.processing_time_ms <= 50
      }
    })
  ]);
}
