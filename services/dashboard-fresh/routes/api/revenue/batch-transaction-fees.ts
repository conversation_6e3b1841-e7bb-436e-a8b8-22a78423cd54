// Batch Transaction Fee API Endpoint
// High-performance batch processing for multiple transaction fee calculations
// Target: <10ms per transaction in batch, 99.9% accuracy

import { Handlers } from "$fresh/server.ts";
import { Client } from "https://deno.land/x/postgres@v0.17.0/mod.ts";
import { transactionFeeCalculator, CustomerTier } from "../../../utils/transactionFeeCalculator.ts";

interface BatchTransactionFeeRequest {
  transactions: Array<{
    partnership_id: string;
    attributed_revenue: number;
    customer_tier: CustomerTier;
    monthly_volume?: number;
    attribution_model?: 'last_touch' | 'first_touch' | 'linear' | 'time_decay';
    attribution_confidence?: number;
    currency_code?: string;
  }>;
  store_results?: boolean; // Whether to store results in database
}

interface BatchTransactionFeeResponse {
  success: boolean;
  data?: Array<{
    transaction_index: number;
    calculation: any;
    success: boolean;
    error?: string;
  }>;
  error?: string;
  processing_time_ms?: number;
  metadata?: {
    total_transactions: number;
    successful_calculations: number;
    failed_calculations: number;
    average_processing_time_per_transaction: number;
    performance_target_met: boolean;
    throughput_per_second: number;
  };
}

// Database connection
const client = new Client({
  user: "postgres",
  password: "password",
  database: "ecommerce_analytics",
  hostname: "localhost",
  port: 5432,
});

export const handler: Handlers = {
  /**
   * POST /api/revenue/batch-transaction-fees
   * Calculate transaction fees for multiple transactions in batch
   */
  async POST(req, _ctx) {
    const startTime = performance.now();
    
    try {
      await client.connect();
      
      const body: BatchTransactionFeeRequest = await req.json();
      
      // Validate request
      if (!body.transactions || !Array.isArray(body.transactions) || body.transactions.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          error: "Missing or empty transactions array"
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Limit batch size for performance
      if (body.transactions.length > 1000) {
        return new Response(JSON.stringify({
          success: false,
          error: "Batch size limited to 1000 transactions. Use pagination for larger batches."
        }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }

      // Process transactions in parallel batches for optimal performance
      const batchSize = 50; // Process 50 transactions at a time
      const results: Array<{
        transaction_index: number;
        calculation: any;
        success: boolean;
        error?: string;
      }> = [];

      let successfulCalculations = 0;
      let failedCalculations = 0;

      // Process in batches to avoid overwhelming the system
      for (let i = 0; i < body.transactions.length; i += batchSize) {
        const batch = body.transactions.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (transaction, batchIndex) => {
          const transactionIndex = i + batchIndex;
          
          try {
            // Validate individual transaction
            if (!transaction.partnership_id || transaction.attributed_revenue === undefined || !transaction.customer_tier) {
              throw new Error(`Transaction ${transactionIndex}: Missing required fields`);
            }

            const calculation = await transactionFeeCalculator.calculateTransactionFee(
              transaction.partnership_id,
              transaction.attributed_revenue,
              transaction.customer_tier,
              transaction.monthly_volume || 0,
              transaction.attribution_model || 'last_touch',
              transaction.attribution_confidence || 100,
              transaction.currency_code || 'USD'
            );

            // Store in database if requested
            if (body.store_results) {
              await storeTransactionCalculation(client, calculation, transactionIndex);
            }

            return {
              transaction_index: transactionIndex,
              calculation,
              success: true
            };

          } catch (error) {
            console.error(`Transaction ${transactionIndex} calculation failed:`, error);
            
            return {
              transaction_index: transactionIndex,
              calculation: null,
              success: false,
              error: error.message
            };
          }
        });

        // Wait for current batch to complete
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Update counters
        successfulCalculations += batchResults.filter(r => r.success).length;
        failedCalculations += batchResults.filter(r => !r.success).length;
      }

      const processingTime = performance.now() - startTime;
      const averageProcessingTime = processingTime / body.transactions.length;
      const performanceTargetMet = averageProcessingTime <= 10; // 10ms per transaction target
      const throughputPerSecond = (body.transactions.length / processingTime) * 1000;

      const response: BatchTransactionFeeResponse = {
        success: failedCalculations === 0,
        data: results,
        processing_time_ms: Math.round(processingTime),
        metadata: {
          total_transactions: body.transactions.length,
          successful_calculations: successfulCalculations,
          failed_calculations: failedCalculations,
          average_processing_time_per_transaction: Math.round(averageProcessingTime * 100) / 100,
          performance_target_met: performanceTargetMet,
          throughput_per_second: Math.round(throughputPerSecond)
        }
      };

      // Log performance metrics
      console.log(`Batch transaction fee calculation completed:`, {
        total_transactions: body.transactions.length,
        successful: successfulCalculations,
        failed: failedCalculations,
        processing_time_ms: Math.round(processingTime),
        avg_time_per_transaction: Math.round(averageProcessingTime * 100) / 100,
        throughput_per_second: Math.round(throughputPerSecond),
        performance_target_met: performanceTargetMet
      });

      return new Response(JSON.stringify(response), {
        status: failedCalculations === 0 ? 200 : 207, // 207 Multi-Status for partial success
        headers: { "Content-Type": "application/json" }
      });

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      console.error("Batch transaction fee calculation failed:", error);
      
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        processing_time_ms: Math.round(processingTime)
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    } finally {
      await client.end();
    }
  }
};

/**
 * Store transaction calculation in database with batch optimization
 */
async function storeTransactionCalculation(
  client: Client, 
  calculation: any,
  transactionIndex: number
): Promise<void> {
  const query = `
    INSERT INTO marketplace_transactions (
      partnership_id,
      source_tenant_id,
      target_tenant_id,
      attributed_revenue,
      currency_code,
      customer_tier,
      base_fee_percentage,
      volume_discount_percentage,
      final_fee_percentage,
      platform_commission,
      partner_payout,
      attribution_model,
      attribution_confidence,
      status,
      processing_time_ms,
      calculated_at,
      transaction_metadata
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
    )
  `;

  // For now, use placeholder tenant IDs - in production these would come from the partnership
  const sourceTenantId = crypto.randomUUID();
  const targetTenantId = crypto.randomUUID();

  await client.queryArray(query, [
    calculation.partnership_id,
    sourceTenantId,
    targetTenantId,
    calculation.attributed_revenue,
    calculation.currency_code,
    calculation.customer_tier,
    calculation.base_fee_percentage,
    calculation.volume_discount_percentage,
    calculation.final_fee_percentage,
    calculation.platform_commission,
    calculation.partner_payout,
    calculation.attribution_model,
    calculation.attribution_confidence,
    'calculated',
    calculation.processing_time_ms,
    calculation.calculation_timestamp.toISOString(),
    JSON.stringify({
      batch_index: transactionIndex,
      calculation_timestamp: calculation.calculation_timestamp.toISOString(),
      performance_metrics: {
        processing_time_ms: calculation.processing_time_ms,
        target_met: calculation.processing_time_ms <= 50
      }
    })
  ]);
}
