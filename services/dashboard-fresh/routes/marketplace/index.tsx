// Marketplace Portal - Main Dashboard
// Provides overview of marketplace activity, partnerships, and opportunities

import { defineRoute } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import { MarketplaceUser, Partnership, MarketplaceOpportunity } from "../../types/marketplace.ts";
import DashboardLayout from "../../components/layout/DashboardLayout.tsx";
import D3EnhancedRevenueTrend from "../../islands/charts/D3EnhancedRevenueTrend.tsx";
import InteractiveDataProducts from "../../islands/marketplace/InteractiveDataProducts.tsx";
import InteractivePremiumMatching from "../../islands/marketplace/InteractivePremiumMatching.tsx";

export default defineRoute((_req, _ctx) => {
  // Mock data for development/testing
  const mockUser: MarketplaceUser = {
    id: "mock-user-1",
    email: "<EMAIL>",
    firstName: "Demo",
    lastName: "User",
    companyName: "Demo Company",
    role: "marketplace_participant",
    tenant_id: "mock-tenant-1",
    isActive: true,
    emailVerified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    roles: ["marketplace_participant", "partner_seeker"],
    marketplace_tier: "advanced",
    data_sharing_consent: true,
    partner_access_level: "collaborate",
    network_permissions: {
      can_view_benchmarks: true,
      can_initiate_partnerships: true,
      can_access_shared_analytics: true,
      can_create_data_products: false,
      can_manage_revenue_sharing: false
    },
    privacy_settings: {
      allow_partner_discovery: true,
      share_anonymized_metrics: true,
      participate_in_benchmarks: true
    }
  };

  const mockPartnerships: Partnership[] = [
    {
      id: "partnership-1",
      initiator_tenant_id: "mock-tenant-1",
      partner_tenant_id: "partner-1",
      partnership_type: "referral",
      status: "active",
      revenue_share_percentage: 15,
      commission_rate: 5,
      attribution_window_days: 30,
      partnership_terms: {},
      performance_metrics: {},
      created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      activated_at: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
      initiator_company_name: "Demo Company",
      partner_company_name: "TechCorp Solutions",
      compatibility_score: 85
    },
    {
      id: "partnership-2",
      initiator_tenant_id: "mock-tenant-1",
      partner_tenant_id: "partner-2",
      partnership_type: "revenue_sharing",
      status: "active",
      revenue_share_percentage: 20,
      commission_rate: 8,
      attribution_window_days: 45,
      partnership_terms: {},
      performance_metrics: {},
      created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      activated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
      initiator_company_name: "Demo Company",
      partner_company_name: "RetailMax Inc",
      compatibility_score: 72
    }
  ];

  const mockOpportunities: MarketplaceOpportunity[] = [
    {
      opportunity_id: "opp-1",
      opportunity_type: "partnership",
      title: "High-Value Retail Partnership",
      description: "Partner with leading retail company for cross-promotion opportunities",
      potential_revenue: 25000,
      effort_required: "medium",
      time_to_realize: 30,
      confidence_score: 85,
      recommended_actions: [
        { action: "Initiate contact", priority: "high", estimated_impact: 15000 },
        { action: "Prepare partnership proposal", priority: "medium", estimated_impact: 8000 }
      ],
      related_partners: ["partner-2"]
    },
    {
      opportunity_id: "opp-2",
      opportunity_type: "optimization",
      title: "Conversion Rate Optimization",
      description: "Optimize partner referral conversion rates through A/B testing",
      potential_revenue: 12000,
      effort_required: "low",
      time_to_realize: 14,
      confidence_score: 92,
      recommended_actions: [
        { action: "Set up A/B tests", priority: "high", estimated_impact: 8000 },
        { action: "Analyze conversion funnels", priority: "medium", estimated_impact: 4000 }
      ]
    }
  ];

  const mockPerformanceSummary = {
    active_partnerships: mockPartnerships.filter(p => p.status === 'active').length,
    total_revenue_30d: 45000,
    total_commission_30d: 3200,
    conversion_rate: 12.5,
    top_performing_partnership: mockPartnerships[0]
  };

  // Mock revenue trend data for the enhanced chart
  const mockRevenueTrendData = Array.from({ length: 90 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (89 - i));
    const baseRevenue = 1000 + Math.sin(i / 10) * 500 + Math.random() * 200;
    return {
      timestamp: date.toISOString(),
      revenue: Math.max(0, baseRevenue + (i * 10)), // Growing trend
      events: Math.floor(50 + Math.random() * 100),
      conversions: Math.floor(5 + Math.random() * 15),
      partners: Math.floor(2 + Math.random() * 3),
      commission: Math.floor(baseRevenue * 0.05)
    };
  });

  const user = mockUser;
  const opportunities = mockOpportunities;
  const performance_summary = mockPerformanceSummary;

  // Check marketplace access
  if (!user.roles.includes('marketplace_participant')) {
    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Marketplace Access Required - E-commerce Analytics</title>
        </Head>
        <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div class="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
            <div class="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Marketplace Access Required
            </h2>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              Upgrade to Advanced tier or higher to access marketplace features and discover partnership opportunities.
            </p>
            <div class="space-y-3">
              <a 
                href="/settings?tab=billing" 
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 inline-block"
              >
                Upgrade Plan
              </a>
              <a 
                href="/analytics" 
                class="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 inline-block"
              >
                Back to Analytics
              </a>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout user={user} activeSection="marketplace">
      <Head>
        <title>Marketplace Executive Dashboard - E-commerce Analytics</title>
        <meta name="description" content="Strategic overview of marketplace performance, partnerships, and growth opportunities" />
      </Head>

      <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Executive Header */}
        <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                  Executive Dashboard
                </h1>
                <p class="mt-2 text-gray-600 dark:text-gray-300">
                  Strategic overview of marketplace performance and growth opportunities
                </p>
              </div>

              {/* Executive Quick Actions */}
              <div class="flex space-x-3">
                <a
                  href="/marketplace/discover"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Discover Partners
                </a>

                <a
                  href="/marketplace/partnerships"
                  class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Manage Partnerships
                </a>

                <a
                  href="/marketplace/analytics"
                  class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Analytics Deep Dive
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Executive Dashboard Content */}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

          {/* Revenue Features Overview */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

            {/* Data Products Revenue */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Data Products</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">$175K</p>
                  <p class="text-xs text-green-600 dark:text-green-400">ARR Potential</p>
                </div>
              </div>
            </div>

            {/* Premium Matching Revenue */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Premium Matching</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">$160K</p>
                  <p class="text-xs text-green-600 dark:text-green-400">ARR Potential</p>
                </div>
              </div>
            </div>

            {/* Volume Discounts */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Volume Discounts</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">30%</p>
                  <p class="text-xs text-green-600 dark:text-green-400">Max Discount</p>
                </div>
              </div>
            </div>

            {/* Total Revenue Potential */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Revenue</p>
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">$335K</p>
                  <p class="text-xs text-green-600 dark:text-green-400">Additional ARR</p>
                </div>
              </div>
            </div>
          </div>

          {/* Revenue Features Sections */}
          <div class="space-y-8">

            {/* Revenue Analytics Dashboard */}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                  Revenue Analytics & Volume Discounts
                </h2>
                <p class="mt-1 text-gray-600 dark:text-gray-300">
                  Transaction fees, volume discounts, and revenue metrics
                </p>
              </div>
              <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                    <div class="text-2xl font-bold">$188K</div>
                    <div class="text-blue-100 text-sm">Total Revenue</div>
                    <div class="text-blue-200 text-xs mt-1">+18.5% vs last period</div>
                  </div>
                  <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                    <div class="text-2xl font-bold">$125K</div>
                    <div class="text-green-100 text-sm">Transaction Fees</div>
                    <div class="text-green-200 text-xs mt-1">4,500 transactions</div>
                  </div>
                  <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                    <div class="text-2xl font-bold">$8.5K</div>
                    <div class="text-purple-100 text-sm">Volume Savings</div>
                    <div class="text-purple-200 text-xs mt-1">Customer discounts applied</div>
                  </div>
                  <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white">
                    <div class="text-2xl font-bold">2.8%</div>
                    <div class="text-orange-100 text-sm">Avg Fee Rate</div>
                    <div class="text-orange-200 text-xs mt-1">After volume discounts</div>
                  </div>
                </div>

                {/* Volume Discount Tiers */}
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Volume Discount Tiers
                  </h3>
                  <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                      <thead>
                        <tr class="border-b border-gray-200 dark:border-gray-600">
                          <th class="text-left py-2 text-gray-600 dark:text-gray-300">Tier</th>
                          <th class="text-left py-2 text-gray-600 dark:text-gray-300">Base Fee</th>
                          <th class="text-left py-2 text-gray-600 dark:text-gray-300">Monthly Volume</th>
                          <th class="text-left py-2 text-gray-600 dark:text-gray-300">Discount</th>
                          <th class="text-left py-2 text-gray-600 dark:text-gray-300">Final Fee</th>
                          <th class="text-left py-2 text-gray-600 dark:text-gray-300">Monthly Savings</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr class="border-b border-gray-100 dark:border-gray-600">
                          <td class="py-3 font-medium text-gray-900 dark:text-white">Advanced</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">5.0%</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">$50K+</td>
                          <td class="py-3 text-gray-400">None</td>
                          <td class="py-3 font-medium text-gray-900 dark:text-white">5.0%</td>
                          <td class="py-3 text-gray-400">$0</td>
                        </tr>
                        <tr class="border-b border-gray-100 dark:border-gray-600">
                          <td class="py-3 font-medium text-gray-900 dark:text-white">Enterprise</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">3.0%</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">$150K+</td>
                          <td class="py-3 text-green-600 dark:text-green-400 font-medium">10%</td>
                          <td class="py-3 font-medium text-gray-900 dark:text-white">2.7%</td>
                          <td class="py-3 text-green-600 dark:text-green-400 font-medium">$450</td>
                        </tr>
                        <tr class="border-b border-gray-100 dark:border-gray-600">
                          <td class="py-3 font-medium text-gray-900 dark:text-white">Enterprise</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">3.0%</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">$600K+</td>
                          <td class="py-3 text-green-600 dark:text-green-400 font-medium">20%</td>
                          <td class="py-3 font-medium text-gray-900 dark:text-white">2.4%</td>
                          <td class="py-3 text-green-600 dark:text-green-400 font-medium">$3,600</td>
                        </tr>
                        <tr class="border-b border-gray-100 dark:border-gray-600">
                          <td class="py-3 font-medium text-gray-900 dark:text-white">Strategic</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">1.0%</td>
                          <td class="py-3 text-gray-600 dark:text-gray-300">$1.5M+</td>
                          <td class="py-3 text-green-600 dark:text-green-400 font-medium">30%</td>
                          <td class="py-3 font-medium text-gray-900 dark:text-white">0.7%</td>
                          <td class="py-3 text-green-600 dark:text-green-400 font-medium">$4,500</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Data Products Marketplace */}
            <InteractiveDataProducts
              userTier={user.marketplace_tier === 'none' || user.marketplace_tier === 'basic' ? 'core' : user.marketplace_tier}
            />

            {/* Interactive Premium Matching Services */}
            <InteractivePremiumMatching
              userTier={user.marketplace_tier === 'none' || user.marketplace_tier === 'basic' ? 'core' : user.marketplace_tier}
            />

          </div>
        </div>
      </div>
    </DashboardLayout>
  );
});
