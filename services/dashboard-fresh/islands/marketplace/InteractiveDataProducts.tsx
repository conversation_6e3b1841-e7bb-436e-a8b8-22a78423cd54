// Interactive Data Products Marketplace Island
// Fully functional subscription management with API integration
// Target: $175K ARR from data products revenue stream

import { useEffect, useState } from "preact/hooks";

interface DataProduct {
  id: string;
  product_name: string;
  product_description: string;
  product_category: string;
  creator_tier: string;
  base_price_monthly: string;
  pricing_model: string;
  is_featured: boolean;
  data_format: string;
  update_frequency: string;
  accuracy_percentage: string;
  creator_rating: string;
  tags: string[];
  total_subscribers: number;
}

interface SubscriptionRequest {
  product_id: string;
  subscription_tier: 'basic' | 'premium' | 'enterprise';
  billing_cycle: 'monthly' | 'annual';
}

interface InteractiveDataProductsProps {
  userTier: 'core' | 'advanced' | 'enterprise' | 'strategic';
}

export default function InteractiveDataProducts({ userTier }: InteractiveDataProductsProps) {
  const [products, setProducts] = useState<DataProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscribingTo, setSubscribingTo] = useState<string | null>(null);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<DataProduct | null>(null);
  const [subscriptionForm, setSubscriptionForm] = useState({
    subscription_tier: 'basic' as const,
    billing_cycle: 'monthly' as const,
    payment_method: 'credit_card',
    auto_renewal: true
  });

  // Fetch data products from API
  useEffect(() => {
    fetchDataProducts();
  }, []);

  const fetchDataProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/marketplace/data-products?featured=true&limit=6');
      const data = await response.json();
      
      if (data.success) {
        setProducts(data.data || []);
      } else {
        setError(data.error || 'Failed to load data products');
      }
    } catch (err) {
      setError('Failed to connect to marketplace API');
      console.error('Data products fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (product: DataProduct) => {
    if (userTier === 'core') {
      alert('Upgrade to Advanced tier or higher to subscribe to data products');
      return;
    }

    setSelectedProduct(product);
    setShowSubscriptionModal(true);
  };

  const processSubscription = async () => {
    if (!selectedProduct) return;

    try {
      setSubscribingTo(selectedProduct.id);
      
      const subscriptionData = {
        product_id: selectedProduct.id,
        subscriber_tier: userTier,
        subscription_tier: subscriptionForm.subscription_tier,
        billing_cycle: subscriptionForm.billing_cycle,
        payment_method: subscriptionForm.payment_method,
        auto_renewal: subscriptionForm.auto_renewal,
        monthly_price: parseFloat(selectedProduct.base_price_monthly)
      };

      const response = await fetch('/api/marketplace/data-products/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(subscriptionData)
      });

      const data = await response.json();
      
      if (data.success) {
        alert(`Successfully subscribed to ${selectedProduct.product_name}!\nSubscription ID: ${data.data.subscription_id}\nNext billing: ${data.data.next_billing_date}`);
        setShowSubscriptionModal(false);
        setSelectedProduct(null);
        // Refresh products to show updated subscriber count
        fetchDataProducts();
      } else {
        alert(`Subscription failed: ${data.error}`);
      }
    } catch (err) {
      alert('Failed to process subscription');
      console.error('Subscription error:', err);
    } finally {
      setSubscribingTo(null);
    }
  };

  const getProductIcon = (category: string) => {
    switch (category) {
      case 'industry_benchmarks':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      case 'trend_analysis':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'competitive_intelligence':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
          </svg>
        );
    }
  };

  const getRevenueShare = (creatorTier: string) => {
    return creatorTier === 'strategic' ? '80/20' : '70/30';
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Data Products Marketplace
          </h2>
          <p className="mt-1 text-gray-600 dark:text-gray-300">
            Loading premium analytics data products...
          </p>
        </div>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-3"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-4"></div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Data Products Marketplace
            </h2>
            <p className="mt-1 text-gray-600 dark:text-gray-300">
              Discover and subscribe to premium analytics data products
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-green-600 dark:text-green-400 font-medium">
              $175K ARR Potential
            </span>
            <button
              onClick={fetchDataProducts}
              className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {error ? (
          <div className="text-center py-8">
            <div className="text-red-600 dark:text-red-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-4">{error}</p>
            <button
              onClick={fetchDataProducts}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300">No data products available</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <div key={product.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="text-blue-600 dark:text-blue-400">
                    {getProductIcon(product.product_category)}
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                    {product.product_name}
                  </h3>
                  {product.is_featured && (
                    <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs px-2 py-1 rounded-full font-medium">
                      Featured
                    </span>
                  )}
                </div>
                
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-2">
                  {product.product_description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-3">
                  <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded">
                    {product.data_format.toUpperCase()}
                  </span>
                  <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded">
                    {product.update_frequency}
                  </span>
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded">
                    {getRevenueShare(product.creator_tier)} split
                  </span>
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded">
                    ⭐ {product.creator_rating}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      ${parseFloat(product.base_price_monthly).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      per month • {product.total_subscribers} subscribers
                    </div>
                  </div>
                  <button
                    onClick={() => handleSubscribe(product)}
                    disabled={subscribingTo === product.id || userTier === 'core'}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                      userTier === 'core'
                        ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                        : subscribingTo === product.id
                        ? 'bg-blue-400 text-white cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {subscribingTo === product.id ? 'Processing...' : 'Subscribe'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Subscription Modal */}
      {showSubscriptionModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Subscribe to {selectedProduct.product_name}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Subscription Tier
                </label>
                <select
                  value={subscriptionForm.subscription_tier}
                  onChange={(e) => setSubscriptionForm({...subscriptionForm, subscription_tier: e.currentTarget.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="basic">Basic Access</option>
                  <option value="premium">Premium Access (+API)</option>
                  <option value="enterprise">Enterprise (+Custom Reports)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Billing Cycle
                </label>
                <select
                  value={subscriptionForm.billing_cycle}
                  onChange={(e) => setSubscriptionForm({...subscriptionForm, billing_cycle: e.currentTarget.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="monthly">Monthly</option>
                  <option value="annual">Annual (10% discount)</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="auto_renewal"
                  checked={subscriptionForm.auto_renewal}
                  onChange={(e) => setSubscriptionForm({...subscriptionForm, auto_renewal: e.currentTarget.checked})}
                  className="mr-2"
                />
                <label htmlFor="auto_renewal" className="text-sm text-gray-700 dark:text-gray-300">
                  Enable auto-renewal
                </label>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <div className="flex justify-between">
                    <span>Monthly Price:</span>
                    <span className="font-medium">${selectedProduct.base_price_monthly}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Revenue Share:</span>
                    <span className="font-medium">{getRevenueShare(selectedProduct.creator_tier)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowSubscriptionModal(false);
                  setSelectedProduct(null);
                }}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={processSubscription}
                disabled={subscribingTo !== null}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium disabled:opacity-50 transition-colors duration-200"
              >
                {subscribingTo ? 'Processing...' : 'Subscribe Now'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
