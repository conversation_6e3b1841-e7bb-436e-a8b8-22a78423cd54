// Revenue Analytics Island Component
// Volume discounts, transaction fees, and revenue metrics dashboard
// Target: Enhanced transaction fees with volume discounts

import { useEffect, useState } from "preact/hooks";

interface RevenueMetrics {
  transaction_fees: {
    total_revenue: number;
    volume_discount_savings: number;
    average_fee_percentage: number;
    transactions_count: number;
  };
  data_products: {
    total_revenue: number;
    creator_revenue: number;
    platform_revenue: number;
    active_subscriptions: number;
  };
  premium_matching: {
    total_revenue: number;
    completed_requests: number;
    success_rate: number;
    average_satisfaction: number;
  };
  total_marketplace_revenue: number;
}

interface VolumeDiscountTier {
  tier: string;
  base_fee: number;
  monthly_volume: number;
  discount_percentage: number;
  final_fee: number;
  savings: number;
}

interface RevenueAnalyticsProps {
  userTier: 'core' | 'advanced' | 'enterprise' | 'strategic';
}

export default function RevenueAnalytics({ userTier }: RevenueAnalyticsProps) {
  const [metrics, setMetrics] = useState<RevenueMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');

  // Volume discount tiers for display
  const volumeDiscountTiers: VolumeDiscountTier[] = [
    {
      tier: 'Advanced',
      base_fee: 5.0,
      monthly_volume: 50000,
      discount_percentage: 0,
      final_fee: 5.0,
      savings: 0
    },
    {
      tier: 'Enterprise',
      base_fee: 3.0,
      monthly_volume: 150000,
      discount_percentage: 10,
      final_fee: 2.7,
      savings: 450
    },
    {
      tier: 'Enterprise',
      base_fee: 3.0,
      monthly_volume: 600000,
      discount_percentage: 20,
      final_fee: 2.4,
      savings: 3600
    },
    {
      tier: 'Strategic',
      base_fee: 1.0,
      monthly_volume: 1500000,
      discount_percentage: 30,
      final_fee: 0.7,
      savings: 4500
    }
  ];

  // Fetch revenue analytics
  useEffect(() => {
    fetchRevenueAnalytics();
  }, [selectedTimeframe]);

  const fetchRevenueAnalytics = async () => {
    try {
      setLoading(true);
      
      // Mock data for demonstration - in production this would come from the API
      const mockMetrics: RevenueMetrics = {
        transaction_fees: {
          total_revenue: 125000,
          volume_discount_savings: 8500,
          average_fee_percentage: 2.8,
          transactions_count: 4500
        },
        data_products: {
          total_revenue: 45000,
          creator_revenue: 32000,
          platform_revenue: 13000,
          active_subscriptions: 28
        },
        premium_matching: {
          total_revenue: 18500,
          completed_requests: 12,
          success_rate: 85.5,
          average_satisfaction: 4.3
        },
        total_marketplace_revenue: 188500
      };

      setMetrics(mockMetrics);
    } catch (err) {
      setError('Failed to load revenue analytics');
      console.error('Revenue analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateTransactionFee = async () => {
    try {
      const response = await fetch('/api/revenue/transaction-fees', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          partnership_id: 'demo-partnership',
          attributed_revenue: 10000,
          customer_tier: userTier,
          monthly_volume: 150000
        })
      });

      const data = await response.json();
      
      if (data.success) {
        alert(`Transaction Fee Calculation:
Fee Percentage: ${data.calculation.final_fee_percentage}%
Platform Commission: $${data.calculation.platform_commission}
Volume Discount: ${data.calculation.volume_discount_percentage}%
Processing Time: ${data.processing_time_ms}ms`);
      } else {
        alert(`Calculation failed: ${data.error}`);
      }
    } catch (err) {
      alert('Failed to calculate transaction fee');
      console.error('Transaction fee calculation error:', err);
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Revenue Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Revenue Analytics
              </h2>
              <p className="mt-1 text-gray-600 dark:text-gray-300">
                Transaction fees, volume discounts, and marketplace revenue
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.currentTarget.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
              <button
                onClick={calculateTransactionFee}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium text-sm"
              >
                Test Fee Calculator
              </button>
            </div>
          </div>
        </div>

        {error ? (
          <div className="p-6 text-center">
            <div className="text-red-600 dark:text-red-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300">{error}</p>
          </div>
        ) : metrics && (
          <div className="p-6">
            {/* Revenue Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                <div className="text-2xl font-bold">
                  ${(metrics.total_marketplace_revenue / 1000).toFixed(0)}K
                </div>
                <div className="text-blue-100 text-sm">Total Revenue</div>
                <div className="text-blue-200 text-xs mt-1">+18.5% vs last period</div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                <div className="text-2xl font-bold">
                  ${(metrics.transaction_fees.total_revenue / 1000).toFixed(0)}K
                </div>
                <div className="text-green-100 text-sm">Transaction Fees</div>
                <div className="text-green-200 text-xs mt-1">{metrics.transaction_fees.transactions_count} transactions</div>
              </div>

              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                <div className="text-2xl font-bold">
                  ${(metrics.transaction_fees.volume_discount_savings / 1000).toFixed(1)}K
                </div>
                <div className="text-purple-100 text-sm">Volume Savings</div>
                <div className="text-purple-200 text-xs mt-1">Customer discounts applied</div>
              </div>

              <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white">
                <div className="text-2xl font-bold">
                  {metrics.transaction_fees.average_fee_percentage.toFixed(1)}%
                </div>
                <div className="text-orange-100 text-sm">Avg Fee Rate</div>
                <div className="text-orange-200 text-xs mt-1">After volume discounts</div>
              </div>
            </div>

            {/* Revenue Breakdown */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Transaction Fees */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  Transaction Fees
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Total Revenue</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      ${metrics.transaction_fees.total_revenue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Volume Savings</span>
                    <span className="font-medium text-green-600 dark:text-green-400">
                      -${metrics.transaction_fees.volume_discount_savings.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Transactions</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {metrics.transaction_fees.transactions_count.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Data Products */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <svg className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                  </svg>
                  Data Products
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Total Revenue</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      ${metrics.data_products.total_revenue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Creator Share</span>
                    <span className="font-medium text-blue-600 dark:text-blue-400">
                      ${metrics.data_products.creator_revenue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Subscriptions</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {metrics.data_products.active_subscriptions}
                    </span>
                  </div>
                </div>
              </div>

              {/* Premium Matching */}
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <svg className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Premium Matching
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Total Revenue</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      ${metrics.premium_matching.total_revenue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Success Rate</span>
                    <span className="font-medium text-green-600 dark:text-green-400">
                      {metrics.premium_matching.success_rate.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Completed</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {metrics.premium_matching.completed_requests}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Volume Discount Tiers */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Volume Discount Tiers
          </h3>
          <p className="mt-1 text-gray-600 dark:text-gray-300">
            Transaction fee discounts based on monthly volume
          </p>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-2 text-gray-600 dark:text-gray-300">Tier</th>
                  <th className="text-left py-2 text-gray-600 dark:text-gray-300">Base Fee</th>
                  <th className="text-left py-2 text-gray-600 dark:text-gray-300">Monthly Volume</th>
                  <th className="text-left py-2 text-gray-600 dark:text-gray-300">Discount</th>
                  <th className="text-left py-2 text-gray-600 dark:text-gray-300">Final Fee</th>
                  <th className="text-left py-2 text-gray-600 dark:text-gray-300">Monthly Savings</th>
                </tr>
              </thead>
              <tbody>
                {volumeDiscountTiers.map((tier, index) => (
                  <tr key={index} className={`border-b border-gray-100 dark:border-gray-700 ${
                    tier.tier.toLowerCase() === userTier ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}>
                    <td className="py-3 font-medium text-gray-900 dark:text-white">
                      {tier.tier}
                      {tier.tier.toLowerCase() === userTier && (
                        <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                          Your Tier
                        </span>
                      )}
                    </td>
                    <td className="py-3 text-gray-600 dark:text-gray-300">{tier.base_fee}%</td>
                    <td className="py-3 text-gray-600 dark:text-gray-300">
                      ${(tier.monthly_volume / 1000).toFixed(0)}K+
                    </td>
                    <td className="py-3">
                      {tier.discount_percentage > 0 ? (
                        <span className="text-green-600 dark:text-green-400 font-medium">
                          {tier.discount_percentage}%
                        </span>
                      ) : (
                        <span className="text-gray-400">None</span>
                      )}
                    </td>
                    <td className="py-3 font-medium text-gray-900 dark:text-white">
                      {tier.final_fee}%
                    </td>
                    <td className="py-3">
                      {tier.savings > 0 ? (
                        <span className="text-green-600 dark:text-green-400 font-medium">
                          ${tier.savings.toLocaleString()}
                        </span>
                      ) : (
                        <span className="text-gray-400">$0</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
