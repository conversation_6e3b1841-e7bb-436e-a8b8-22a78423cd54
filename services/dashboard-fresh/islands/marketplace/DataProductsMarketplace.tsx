// Data Products Marketplace Island Component
// Interactive catalog for data products with subscription management
// Target: $175K ARR from data products revenue stream

import { useEffect, useState } from "preact/hooks";

interface DataProduct {
  id: string;
  product_name: string;
  product_description: string;
  product_category: 'industry_benchmarks' | 'trend_analysis' | 'competitive_intelligence' | 'custom_analytics';
  creator_tier: 'enterprise' | 'strategic';
  base_price_monthly: number;
  pricing_model: 'subscription' | 'one_time' | 'usage_based';
  is_active: boolean;
  is_featured: boolean;
  data_format: 'json' | 'csv' | 'parquet' | 'api';
  update_frequency: 'real_time' | 'hourly' | 'daily' | 'weekly' | 'monthly';
  total_subscribers: number;
  monthly_revenue: number;
  creator_rating?: number;
  tags?: string[];
  created_at: string;
}

interface DataProductsMarketplaceProps {
  userTier: 'core' | 'advanced' | 'enterprise' | 'strategic';
}

export default function DataProductsMarketplace({ userTier }: DataProductsMarketplaceProps) {
  const [products, setProducts] = useState<DataProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [subscribing, setSubscribing] = useState<string | null>(null);

  // Fetch data products from API
  useEffect(() => {
    fetchDataProducts();
  }, [selectedCategory]);

  const fetchDataProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') {
        params.append('category', selectedCategory);
      }
      
      const response = await fetch(`/api/marketplace/data-products?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setProducts(data.data || []);
      } else {
        setError(data.error || 'Failed to load data products');
      }
    } catch (err) {
      setError('Failed to connect to marketplace API');
      console.error('Data products fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (productId: string) => {
    if (userTier === 'core') {
      alert('Upgrade to Advanced tier or higher to subscribe to data products');
      return;
    }

    try {
      setSubscribing(productId);
      const response = await fetch('/api/marketplace/data-products/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          product_id: productId,
          subscriber_tier: userTier,
          subscription_type: 'monthly'
        })
      });

      const data = await response.json();
      
      if (data.success) {
        alert('Successfully subscribed to data product!');
        fetchDataProducts(); // Refresh the list
      } else {
        alert(`Subscription failed: ${data.error}`);
      }
    } catch (err) {
      alert('Failed to subscribe to data product');
      console.error('Subscription error:', err);
    } finally {
      setSubscribing(null);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'industry_benchmarks':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      case 'trend_analysis':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'competitive_intelligence':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
          </svg>
        );
    }
  };

  const getRevenueShareInfo = (creatorTier: string) => {
    return creatorTier === 'strategic' ? '80/20 split' : '70/30 split';
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Data Products Marketplace
            </h2>
            <p className="mt-1 text-gray-600 dark:text-gray-300">
              Discover and subscribe to premium analytics data products
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-green-600 dark:text-green-400 font-medium">
              $175K ARR Potential
            </span>
          </div>
        </div>

        {/* Category Filter */}
        <div className="mt-4 flex flex-wrap gap-2">
          {['all', 'industry_benchmarks', 'trend_analysis', 'competitive_intelligence', 'custom_analytics'].map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {category === 'all' ? 'All Categories' : category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </button>
          ))}
        </div>
      </div>

      <div className="p-6">
        {error ? (
          <div className="text-center py-8">
            <div className="text-red-600 dark:text-red-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300">{error}</p>
            <button
              onClick={fetchDataProducts}
              className="mt-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
            >
              Try Again
            </button>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300">No data products available in this category</p>
          </div>
        ) : (
          <div className="space-y-4">
            {products.map((product) => (
              <div key={product.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="text-blue-600 dark:text-blue-400">
                        {getCategoryIcon(product.product_category)}
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {product.product_name}
                      </h3>
                      {product.is_featured && (
                        <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs px-2 py-1 rounded-full font-medium">
                          Featured
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                      {product.product_description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2 mb-3">
                      <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded">
                        {product.data_format.toUpperCase()}
                      </span>
                      <span className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded">
                        {product.update_frequency.replace('_', ' ')}
                      </span>
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded">
                        {getRevenueShareInfo(product.creator_tier)}
                      </span>
                      {product.creator_rating && (
                        <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded">
                          ⭐ {product.creator_rating.toFixed(1)}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
                      <span>{product.total_subscribers} subscribers</span>
                      <span>${(product.monthly_revenue / 1000).toFixed(1)}K monthly revenue</span>
                    </div>
                  </div>
                  
                  <div className="ml-4 text-right">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      ${product.base_price_monthly}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      per month
                    </div>
                    
                    <button
                      onClick={() => handleSubscribe(product.id)}
                      disabled={subscribing === product.id || userTier === 'core'}
                      className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                        userTier === 'core'
                          ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                          : subscribing === product.id
                          ? 'bg-gray-400 text-white cursor-not-allowed'
                          : 'bg-blue-600 hover:bg-blue-700 text-white'
                      }`}
                    >
                      {subscribing === product.id ? 'Subscribing...' : 
                       userTier === 'core' ? 'Upgrade Required' : 'Subscribe'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
