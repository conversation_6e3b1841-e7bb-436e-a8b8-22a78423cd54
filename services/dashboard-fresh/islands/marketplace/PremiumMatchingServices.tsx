// Premium Matching Services Island Component
// Pay-per-introduction billing system with success tracking
// Target: $160K ARR from premium matching revenue stream

import { useEffect, useState } from "preact/hooks";

interface PremiumMatchingRequest {
  id: string;
  requester_tier: 'advanced' | 'enterprise' | 'strategic';
  target_industry?: string;
  target_geography?: string;
  target_company_size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  service_type: 'introduction' | 'consultation' | 'custom_matching';
  service_price: number;
  status: 'pending' | 'processing' | 'matched' | 'completed' | 'failed' | 'cancelled';
  matched_partners_count: number;
  introduction_success_rate?: number;
  customer_satisfaction_score?: number;
  success_achieved: boolean;
  success_bonus_amount?: number;
  created_at: string;
}

interface PremiumMatchingServicesProps {
  userTier: 'core' | 'advanced' | 'enterprise' | 'strategic';
}

export default function PremiumMatchingServices({ userTier }: PremiumMatchingServicesProps) {
  const [requests, setRequests] = useState<PremiumMatchingRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [creating, setCreating] = useState(false);
  const [formData, setFormData] = useState({
    target_industry: '',
    target_geography: '',
    target_company_size: 'medium' as const,
    service_type: 'introduction' as const,
    matching_criteria: {
      revenue_range: '',
      focus: '',
      channels: [] as string[]
    }
  });

  // Service pricing based on tier and type
  const getServicePrice = (serviceType: string, tier: string) => {
    const prices = {
      introduction: { advanced: 500, enterprise: 1000, strategic: 2000 },
      consultation: { advanced: 1500, enterprise: 2500, strategic: 5000 },
      custom_matching: { advanced: 3000, enterprise: 5000, strategic: 10000 }
    };
    return prices[serviceType as keyof typeof prices]?.[tier as keyof typeof prices.introduction] || 0;
  };

  // Fetch premium matching requests
  useEffect(() => {
    fetchMatchingRequests();
  }, []);

  const fetchMatchingRequests = async () => {
    try {
      setLoading(true);
      // Mock requester ID for demo - in production this would come from auth
      const response = await fetch('/api/marketplace/premium-matching?requester_id=demo-user-123');
      const data = await response.json();
      
      if (data.success) {
        setRequests(data.data || []);
      } else {
        setError(data.error || 'Failed to load matching requests');
      }
    } catch (err) {
      setError('Failed to connect to premium matching API');
      console.error('Premium matching fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRequest = async () => {
    if (userTier === 'core') {
      alert('Upgrade to Advanced tier or higher to access premium matching services');
      return;
    }

    try {
      setCreating(true);
      const response = await fetch('/api/marketplace/premium-matching', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requester_tier: userTier,
          target_industry: formData.target_industry,
          target_geography: formData.target_geography,
          target_company_size: formData.target_company_size,
          service_type: formData.service_type,
          matching_criteria: formData.matching_criteria
        })
      });

      const data = await response.json();
      
      if (data.success) {
        alert('Premium matching request created successfully!');
        setShowCreateForm(false);
        setFormData({
          target_industry: '',
          target_geography: '',
          target_company_size: 'medium',
          service_type: 'introduction',
          matching_criteria: { revenue_range: '', focus: '', channels: [] }
        });
        fetchMatchingRequests(); // Refresh the list
      } else {
        alert(`Request creation failed: ${data.error}`);
      }
    } catch (err) {
      alert('Failed to create premium matching request');
      console.error('Request creation error:', err);
    } finally {
      setCreating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
      case 'processing':
        return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
      case 'matched':
        return 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200';
      case 'pending':
        return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
      case 'failed':
        return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getServiceIcon = (serviceType: string) => {
    switch (serviceType) {
      case 'introduction':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        );
      case 'consultation':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        );
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[1, 2].map((i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Premium Matching Services
            </h2>
            <p className="mt-1 text-gray-600 dark:text-gray-300">
              High-touch partner matching with success guarantees
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-green-600 dark:text-green-400 font-medium">
              $160K ARR Potential
            </span>
            <button
              onClick={() => setShowCreateForm(true)}
              disabled={userTier === 'core'}
              className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                userTier === 'core'
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              Request Matching
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Service Pricing Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {['introduction', 'consultation', 'custom_matching'].map((serviceType) => (
            <div key={serviceType} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <div className="text-blue-600 dark:text-blue-400">
                  {getServiceIcon(serviceType)}
                </div>
                <h3 className="font-medium text-gray-900 dark:text-white capitalize">
                  {serviceType.replace('_', ' ')}
                </h3>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                ${getServicePrice(serviceType, userTier).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                {serviceType === 'introduction' && 'Partner introductions with 24h response'}
                {serviceType === 'consultation' && 'Strategic consultation with matching'}
                {serviceType === 'custom_matching' && 'Custom matching algorithm development'}
              </div>
            </div>
          ))}
        </div>

        {/* Create Request Form */}
        {showCreateForm && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-6 bg-gray-50 dark:bg-gray-700">
            <h3 className="font-medium text-gray-900 dark:text-white mb-4">Create Matching Request</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Target Industry
                </label>
                <input
                  type="text"
                  value={formData.target_industry}
                  onChange={(e) => setFormData({...formData, target_industry: e.currentTarget.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  placeholder="e.g., Technology, Retail, Healthcare"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Geography
                </label>
                <input
                  type="text"
                  value={formData.target_geography}
                  onChange={(e) => setFormData({...formData, target_geography: e.currentTarget.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  placeholder="e.g., North America, Europe, Global"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company Size
                </label>
                <select
                  value={formData.target_company_size}
                  onChange={(e) => setFormData({...formData, target_company_size: e.currentTarget.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="startup">Startup</option>
                  <option value="small">Small (1-50 employees)</option>
                  <option value="medium">Medium (51-500 employees)</option>
                  <option value="large">Large (501-5000 employees)</option>
                  <option value="enterprise">Enterprise (5000+ employees)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Service Type
                </label>
                <select
                  value={formData.service_type}
                  onChange={(e) => setFormData({...formData, service_type: e.currentTarget.value as any})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="introduction">Introduction (${getServicePrice('introduction', userTier)})</option>
                  <option value="consultation">Consultation (${getServicePrice('consultation', userTier)})</option>
                  <option value="custom_matching">Custom Matching (${getServicePrice('custom_matching', userTier)})</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateRequest}
                disabled={creating}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium disabled:opacity-50"
              >
                {creating ? 'Creating...' : `Create Request ($${getServicePrice(formData.service_type, userTier)})`}
              </button>
            </div>
          </div>
        )}

        {/* Matching Requests List */}
        {error ? (
          <div className="text-center py-8">
            <div className="text-red-600 dark:text-red-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300">{error}</p>
          </div>
        ) : requests.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-300">No premium matching requests yet</p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Create your first request to get started</p>
          </div>
        ) : (
          <div className="space-y-4">
            {requests.map((request) => (
              <div key={request.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="text-blue-600 dark:text-blue-400">
                        {getServiceIcon(request.service_type)}
                      </div>
                      <h3 className="font-medium text-gray-900 dark:text-white capitalize">
                        {request.service_type.replace('_', ' ')} Service
                      </h3>
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(request.status)}`}>
                        {request.status.replace('_', ' ')}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-300 mb-2">
                      {request.target_industry && (
                        <div>
                          <span className="font-medium">Industry:</span> {request.target_industry}
                        </div>
                      )}
                      {request.target_geography && (
                        <div>
                          <span className="font-medium">Geography:</span> {request.target_geography}
                        </div>
                      )}
                      {request.target_company_size && (
                        <div>
                          <span className="font-medium">Size:</span> {request.target_company_size}
                        </div>
                      )}
                      <div>
                        <span className="font-medium">Partners:</span> {request.matched_partners_count}
                      </div>
                    </div>

                    {request.introduction_success_rate !== undefined && (
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="text-green-600 dark:text-green-400">
                          Success Rate: {request.introduction_success_rate}%
                        </span>
                        {request.customer_satisfaction_score && (
                          <span className="text-blue-600 dark:text-blue-400">
                            Satisfaction: {request.customer_satisfaction_score}/5
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-4 text-right">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      ${request.service_price.toLocaleString()}
                    </div>
                    {request.success_bonus_amount && request.success_bonus_amount > 0 && (
                      <div className="text-sm text-green-600 dark:text-green-400">
                        +${request.success_bonus_amount} bonus
                      </div>
                    )}
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {new Date(request.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
