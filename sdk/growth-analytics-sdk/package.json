{"name": "@ecommerce-analytics/growth-sdk", "version": "1.0.0", "description": "Complete Growth Analytics SDK for implementing the 4-week growth framework with 15-minute quick start", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "prepublishOnly": "npm run build"}, "keywords": ["analytics", "growth", "ecommerce", "sdk", "tracking", "marketplace", "performance"], "author": "E-commerce Analytics Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "eventemitter3": "^5.0.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.5.0", "rollup": "^4.0.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-commonjs": "^25.0.0", "rollup-plugin-terser": "^7.0.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ecommerce-analytics-saas.git", "directory": "sdk/growth-analytics-sdk"}, "bugs": {"url": "https://github.com/your-org/ecommerce-analytics-saas/issues"}, "homepage": "https://github.com/your-org/ecommerce-analytics-saas#readme", "engines": {"node": ">=16.0.0"}, "publishConfig": {"access": "public"}}