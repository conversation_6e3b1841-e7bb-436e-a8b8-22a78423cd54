#!/usr/bin/env node

/**
 * Performance Validation Runner
 * Executes comprehensive performance validation and generates reports
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class PerformanceValidationRunner {
  constructor() {
    this.results = {
      comprehensive: null,
      claims: null,
      marketplace: null,
      report: null
    };
    
    this.startTime = Date.now();
  }

  async runAllValidations() {
    console.log('🚀 Starting Comprehensive Performance Validation Suite');
    console.log('====================================================');
    console.log(`Started at: ${new Date().toISOString()}\n`);
    
    try {
      // Check prerequisites
      await this.checkPrerequisites();
      
      // Run validation tests
      await this.runComprehensiveValidation();
      await this.runClaimsValidation();
      await this.runMarketplaceValidation();
      
      // Generate comprehensive report
      await this.generateReport();
      
      // Display summary
      this.displaySummary();
      
    } catch (error) {
      console.error('❌ Validation suite failed:', error);
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking Prerequisites...');
    
    const requiredServices = [
      { name: 'Analytics Service', url: process.env.ANALYTICS_URL || 'http://localhost:3002' },
      { name: 'Dashboard Backend', url: process.env.DASHBOARD_URL || 'http://localhost:3000' },
      { name: 'Integration Service', url: process.env.INTEGRATION_URL || 'http://localhost:3001' },
      { name: 'Billing Service', url: process.env.BILLING_URL || 'http://localhost:3003' },
      { name: 'Admin Service', url: process.env.ADMIN_URL || 'http://localhost:3005' },
      { name: 'Link Tracking', url: process.env.LINK_TRACKING_URL || 'http://localhost:8080' }
    ];
    
    const axios = require('axios');
    const serviceStatus = {};
    
    for (const service of requiredServices) {
      try {
        const response = await axios.get(`${service.url}/health`, { timeout: 5000 });
        serviceStatus[service.name] = response.status === 200 ? 'ONLINE' : 'OFFLINE';
        console.log(`   ${service.name}: ✅ ONLINE`);
      } catch (error) {
        serviceStatus[service.name] = 'OFFLINE';
        console.log(`   ${service.name}: ❌ OFFLINE (${error.message})`);
      }
    }
    
    const offlineServices = Object.entries(serviceStatus).filter(([_, status]) => status === 'OFFLINE');
    
    if (offlineServices.length > 0) {
      console.log('\n⚠️  Warning: Some services are offline. Validation may be incomplete.');
      console.log('   Offline services:', offlineServices.map(([name]) => name).join(', '));
      console.log('   Continuing with available services...\n');
    } else {
      console.log('✅ All services are online\n');
    }
  }

  async runComprehensiveValidation() {
    console.log('📊 Running Comprehensive Performance Validation...');
    
    try {
      const ComprehensiveValidator = require('./comprehensive-performance-validation');
      const validator = new ComprehensiveValidator();
      
      await validator.validateAll();
      this.results.comprehensive = validator.results;
      
      console.log('✅ Comprehensive validation completed\n');
    } catch (error) {
      console.log(`❌ Comprehensive validation failed: ${error.message}\n`);
      this.results.comprehensive = { error: error.message };
    }
  }

  async runClaimsValidation() {
    console.log('🎯 Running Performance Claims Validation...');
    
    try {
      const ClaimsValidator = require('./validate-performance-claims');
      const validator = new ClaimsValidator();
      
      await validator.validateAllClaims();
      this.results.claims = {
        validationResults: validator.validationResults,
        discrepancies: validator.discrepancies
      };
      
      console.log('✅ Claims validation completed\n');
    } catch (error) {
      console.log(`❌ Claims validation failed: ${error.message}\n`);
      this.results.claims = { error: error.message };
    }
  }

  async runMarketplaceValidation() {
    console.log('🏪 Running Marketplace Performance Validation...');
    
    try {
      const MarketplaceValidator = require('./validate-marketplace-performance');
      const validator = new MarketplaceValidator();
      
      await validator.validateAll();
      this.results.marketplace = validator.results;
      
      console.log('✅ Marketplace validation completed\n');
    } catch (error) {
      console.log(`❌ Marketplace validation failed: ${error.message}\n`);
      this.results.marketplace = { error: error.message };
    }
  }

  async generateReport() {
    console.log('📋 Generating Comprehensive Validation Report...');
    
    try {
      const ReportGenerator = require('./generate-validation-report');
      const generator = new ReportGenerator();
      
      // Inject our results into the generator
      generator.reportData.detailedResults = {
        comprehensive: this.results.comprehensive,
        claims: this.results.claims?.validationResults,
        marketplace: this.results.marketplace
      };
      
      if (this.results.claims?.discrepancies) {
        generator.reportData.gaps = this.results.claims.discrepancies;
      }
      
      // Generate the report sections
      generator.generateExecutiveSummary();
      generator.generateDetailedAnalysis();
      generator.generateGapAnalysis();
      generator.generateSalesImpactAssessment();
      generator.generateRecommendations();
      
      // Save reports
      await generator.saveReports();
      
      this.results.report = generator.reportData;
      
      console.log('✅ Validation report generated\n');
    } catch (error) {
      console.log(`❌ Report generation failed: ${error.message}\n`);
      this.results.report = { error: error.message };
    }
  }

  displaySummary() {
    const endTime = Date.now();
    const duration = Math.round((endTime - this.startTime) / 1000);
    
    console.log('🎉 Performance Validation Suite Completed');
    console.log('==========================================');
    console.log(`Total Duration: ${duration} seconds`);
    console.log(`Completed at: ${new Date().toISOString()}\n`);
    
    // Display key results
    if (this.results.report && this.results.report.summary) {
      const summary = this.results.report.summary;
      
      console.log('📊 Key Results:');
      console.log('---------------');
      console.log(`Overall Validation Score: ${summary.overallValidationScore}%`);
      console.log(`Claims Validated: ${summary.totalClaimsValidated}`);
      console.log(`Sales Readiness: ${summary.salesReadiness?.status || 'UNKNOWN'}`);
      
      if (summary.performanceHighlights) {
        console.log('\n🚀 Performance Highlights:');
        
        const eventProcessing = summary.performanceHighlights.eventProcessing;
        if (eventProcessing && eventProcessing.actualRate) {
          console.log(`   Event Processing: ${eventProcessing.actualRate} events/sec (${eventProcessing.achievementPercentage}% of target)`);
        }
        
        const queryPerformance = summary.performanceHighlights.queryPerformance;
        if (queryPerformance && queryPerformance.averageTime) {
          console.log(`   Query Performance: ${queryPerformance.averageTime}ms average (target: <${queryPerformance.target}ms)`);
        }
        
        const mlInference = summary.performanceHighlights.mlInference;
        if (mlInference && mlInference.actualRate) {
          console.log(`   ML Inference: ${mlInference.actualRate} predictions/sec (${mlInference.achievementPercentage}% of target)`);
        }
      }
      
      // Display gaps if any
      if (this.results.report.gapAnalysis && this.results.report.gapAnalysis.totalGaps > 0) {
        const gaps = this.results.report.gapAnalysis;
        console.log('\n⚠️  Performance Gaps:');
        console.log(`   Total: ${gaps.totalGaps} (High: ${gaps.highImpactGaps}, Medium: ${gaps.mediumImpactGaps}, Low: ${gaps.lowImpactGaps})`);
      } else {
        console.log('\n✅ No significant performance gaps identified');
      }
      
      // Display sales impact
      if (this.results.report.salesImpact) {
        const sales = this.results.report.salesImpact;
        console.log('\n💼 Sales Impact:');
        console.log(`   Readiness: ${sales.overallReadiness}`);
        console.log(`   Confidence: ${sales.confidenceLevel}`);
        console.log(`   Risk Level: ${sales.riskAssessment?.level || 'UNKNOWN'}`);
      }
    }
    
    console.log('\n📄 Reports Generated:');
    console.log('   - testing/performance/reports/latest-performance-validation.json');
    console.log('   - testing/performance/reports/latest-performance-validation-summary.md');
    
    console.log('\n🎯 Next Steps:');
    if (this.results.report?.salesImpact?.recommendedActions) {
      this.results.report.salesImpact.recommendedActions.forEach((action, index) => {
        console.log(`   ${index + 1}. ${action}`);
      });
    } else {
      console.log('   1. Review detailed validation report');
      console.log('   2. Address any identified performance gaps');
      console.log('   3. Update sales materials with validated metrics');
      console.log('   4. Implement recommended optimizations');
    }
    
    console.log('\n✨ Validation suite completed successfully!');
  }
}

// Run validation suite if called directly
if (require.main === module) {
  const runner = new PerformanceValidationRunner();
  runner.runAllValidations().catch(console.error);
}

module.exports = PerformanceValidationRunner;
