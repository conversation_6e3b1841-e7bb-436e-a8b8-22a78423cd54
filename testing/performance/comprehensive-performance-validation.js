#!/usr/bin/env node

/**
 * Comprehensive Performance Validation Suite
 * Validates all claimed performance metrics against actual platform capabilities
 * 
 * Claimed Metrics to Validate:
 * - 24,390 events/sec processing capability
 * - 6-11ms query response times
 * - 343.52 predictions/second ML inference
 * - Sub-millisecond link tracking
 * - Marketplace ecosystem performance
 * - 97-98% competitive advantage claims
 */

const { performance } = require('perf_hooks');
const axios = require('axios');
const { Pool } = require('pg');
const Redis = require('ioredis');

class ComprehensivePerformanceValidator {
  constructor() {
    this.results = {
      eventProcessing: {},
      queryPerformance: {},
      mlInference: {},
      linkTracking: {},
      marketplace: {},
      competitiveAnalysis: {},
      gaps: [],
      recommendations: []
    };
    
    this.services = {
      analytics: process.env.ANALYTICS_URL || 'http://localhost:3002',
      dashboard: process.env.DASHBOARD_URL || 'http://localhost:3000',
      integration: process.env.INTEGRATION_URL || 'http://localhost:3001',
      billing: process.env.BILLING_URL || 'http://localhost:3003',
      admin: process.env.ADMIN_URL || 'http://localhost:3005',
      linkTracking: process.env.LINK_TRACKING_URL || 'http://localhost:8080'
    };
    
    this.db = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'ecommerce_analytics',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password'
    });
    
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379
    });
  }

  async validateAll() {
    console.log('🚀 Starting Comprehensive Performance Validation');
    console.log('================================================');
    
    try {
      await this.validateEventProcessing();
      await this.validateQueryPerformance();
      await this.validateMLInference();
      await this.validateLinkTracking();
      await this.validateMarketplacePerformance();
      await this.validateCompetitiveAdvantage();
      
      this.generateReport();
      this.identifyGaps();
      this.generateRecommendations();
      
    } catch (error) {
      console.error('❌ Validation failed:', error);
    } finally {
      await this.cleanup();
    }
  }

  async validateEventProcessing() {
    console.log('\n📊 Validating Event Processing (Target: 24,390 events/sec)');
    console.log('--------------------------------------------------------');
    
    const testDurations = [10, 30, 60]; // seconds
    const targetRate = 24390; // events/sec
    
    for (const duration of testDurations) {
      console.log(`\n⏱️  Testing ${duration}s duration...`);
      
      const events = this.generateTestEvents(targetRate * duration);
      const startTime = performance.now();
      
      try {
        // Test batch event ingestion
        const batchSize = 1000;
        const batches = Math.ceil(events.length / batchSize);
        let successfulEvents = 0;
        
        for (let i = 0; i < batches; i++) {
          const batch = events.slice(i * batchSize, (i + 1) * batchSize);
          
          const response = await axios.post(`${this.services.analytics}/api/events/batch`, {
            events: batch,
            tenant_id: 'performance_test'
          }, { timeout: 30000 });
          
          if (response.status === 200) {
            successfulEvents += batch.length;
          }
        }
        
        const endTime = performance.now();
        const actualDuration = (endTime - startTime) / 1000; // seconds
        const actualRate = successfulEvents / actualDuration;
        const achievementPercentage = (actualRate / targetRate) * 100;
        
        this.results.eventProcessing[`${duration}s_test`] = {
          targetRate,
          actualRate: Math.round(actualRate),
          achievementPercentage: Math.round(achievementPercentage),
          successfulEvents,
          totalEvents: events.length,
          duration: actualDuration,
          status: actualRate >= targetRate ? 'PASS' : 'FAIL'
        };
        
        console.log(`   Target: ${targetRate} events/sec`);
        console.log(`   Actual: ${Math.round(actualRate)} events/sec`);
        console.log(`   Achievement: ${Math.round(achievementPercentage)}%`);
        console.log(`   Status: ${actualRate >= targetRate ? '✅ PASS' : '❌ FAIL'}`);
        
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
        this.results.eventProcessing[`${duration}s_test`] = {
          error: error.message,
          status: 'ERROR'
        };
      }
    }
  }

  async validateQueryPerformance() {
    console.log('\n🔍 Validating Query Performance (Target: 6-11ms)');
    console.log('------------------------------------------------');
    
    const queries = [
      {
        name: 'Simple Analytics',
        query: 'SELECT COUNT(*) FROM customer_events WHERE time >= NOW() - INTERVAL \'1 hour\'',
        target: 11 // ms
      },
      {
        name: 'Cohort Analysis',
        query: `SELECT 
          DATE_TRUNC('week', time) as cohort_week,
          COUNT(DISTINCT user_id) as users,
          AVG(revenue) as avg_revenue
        FROM customer_events 
        WHERE time >= NOW() - INTERVAL '12 weeks'
        GROUP BY cohort_week 
        ORDER BY cohort_week`,
        target: 11 // ms
      },
      {
        name: 'Funnel Analysis',
        query: `SELECT 
          event_type,
          COUNT(*) as events,
          COUNT(DISTINCT user_id) as unique_users
        FROM customer_events 
        WHERE time >= NOW() - INTERVAL '24 hours'
        GROUP BY event_type 
        ORDER BY events DESC`,
        target: 11 // ms
      },
      {
        name: 'Real-time Metrics',
        query: `SELECT 
          DATE_TRUNC('minute', time) as minute,
          COUNT(*) as events,
          SUM(revenue) as revenue
        FROM customer_events 
        WHERE time >= NOW() - INTERVAL '1 hour'
        GROUP BY minute 
        ORDER BY minute DESC 
        LIMIT 60`,
        target: 11 // ms
      }
    ];
    
    for (const queryTest of queries) {
      console.log(`\n⏱️  Testing ${queryTest.name}...`);
      
      const iterations = 10;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        try {
          await this.db.query(queryTest.query);
          const endTime = performance.now();
          const duration = endTime - startTime;
          times.push(duration);
        } catch (error) {
          console.log(`   ❌ Query failed: ${error.message}`);
          times.push(null);
        }
      }
      
      const validTimes = times.filter(t => t !== null);
      if (validTimes.length > 0) {
        const avgTime = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
        const minTime = Math.min(...validTimes);
        const maxTime = Math.max(...validTimes);
        const p95Time = validTimes.sort((a, b) => a - b)[Math.floor(validTimes.length * 0.95)];
        
        this.results.queryPerformance[queryTest.name] = {
          target: queryTest.target,
          average: Math.round(avgTime * 100) / 100,
          min: Math.round(minTime * 100) / 100,
          max: Math.round(maxTime * 100) / 100,
          p95: Math.round(p95Time * 100) / 100,
          iterations: validTimes.length,
          status: avgTime <= queryTest.target ? 'PASS' : 'FAIL'
        };
        
        console.log(`   Target: <${queryTest.target}ms`);
        console.log(`   Average: ${Math.round(avgTime * 100) / 100}ms`);
        console.log(`   P95: ${Math.round(p95Time * 100) / 100}ms`);
        console.log(`   Status: ${avgTime <= queryTest.target ? '✅ PASS' : '❌ FAIL'}`);
      } else {
        this.results.queryPerformance[queryTest.name] = {
          error: 'All queries failed',
          status: 'ERROR'
        };
      }
    }
  }

  async validateMLInference() {
    console.log('\n🤖 Validating ML Inference (Target: 343.52 predictions/sec)');
    console.log('----------------------------------------------------------');
    
    const targetRate = 343.52; // predictions/sec
    const testDuration = 30; // seconds
    const totalPredictions = Math.floor(targetRate * testDuration);
    
    try {
      console.log(`\n⏱️  Testing ${totalPredictions} predictions over ${testDuration}s...`);
      
      const startTime = performance.now();
      let successfulPredictions = 0;
      const batchSize = 100;
      const batches = Math.ceil(totalPredictions / batchSize);
      
      for (let i = 0; i < batches; i++) {
        const batch = Array(Math.min(batchSize, totalPredictions - i * batchSize))
          .fill(null)
          .map(() => ({
            user_id: `test_user_${Math.floor(Math.random() * 10000)}`,
            features: {
              total_orders: Math.floor(Math.random() * 50),
              avg_order_value: Math.random() * 500,
              days_since_last_order: Math.floor(Math.random() * 365),
              total_revenue: Math.random() * 5000
            }
          }));
        
        try {
          const response = await axios.post(
            `${this.services.analytics}/api/enhanced-analytics/predictions/churn`,
            {
              predictions: batch,
              tenant_id: 'performance_test'
            },
            { timeout: 10000 }
          );
          
          if (response.status === 200) {
            successfulPredictions += batch.length;
          }
        } catch (error) {
          console.log(`   ⚠️  Batch ${i + 1} failed: ${error.message}`);
        }
      }
      
      const endTime = performance.now();
      const actualDuration = (endTime - startTime) / 1000; // seconds
      const actualRate = successfulPredictions / actualDuration;
      const achievementPercentage = (actualRate / targetRate) * 100;
      
      this.results.mlInference = {
        targetRate,
        actualRate: Math.round(actualRate * 100) / 100,
        achievementPercentage: Math.round(achievementPercentage),
        successfulPredictions,
        totalPredictions,
        duration: actualDuration,
        status: actualRate >= targetRate ? 'PASS' : 'FAIL'
      };
      
      console.log(`   Target: ${targetRate} predictions/sec`);
      console.log(`   Actual: ${Math.round(actualRate * 100) / 100} predictions/sec`);
      console.log(`   Achievement: ${Math.round(achievementPercentage)}%`);
      console.log(`   Status: ${actualRate >= targetRate ? '✅ PASS' : '❌ FAIL'}`);
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      this.results.mlInference = {
        error: error.message,
        status: 'ERROR'
      };
    }
  }

  async validateMarketplacePerformance() {
    console.log('\n🏪 Validating Marketplace Performance');
    console.log('------------------------------------');

    const marketplaceTests = [
      {
        name: 'Partner Discovery',
        endpoint: '/api/marketplace/partners/discover',
        target: 500, // ms
        payload: {
          tenant_id: 'performance_test',
          business_type: 'ecommerce',
          target_audience: 'young_professionals'
        }
      },
      {
        name: 'Revenue Attribution',
        endpoint: '/api/marketplace/revenue/attribution',
        target: 100, // ms
        payload: {
          tenant_id: 'performance_test',
          date_from: '2025-01-01',
          date_to: '2025-01-31'
        }
      },
      {
        name: 'Compatibility Scoring',
        endpoint: '/api/marketplace/compatibility/score',
        target: 200, // ms
        payload: {
          tenant_id: 'performance_test',
          partner_id: 'test_partner_123'
        }
      }
    ];

    for (const test of marketplaceTests) {
      console.log(`\n⏱️  Testing ${test.name}...`);

      const iterations = 10;
      const times = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();

        try {
          await axios.post(`${this.services.integration}${test.endpoint}`, test.payload, {
            timeout: test.target * 2
          });

          const endTime = performance.now();
          times.push(endTime - startTime);
        } catch (error) {
          console.log(`   ⚠️  Request ${i + 1} failed: ${error.message}`);
          times.push(null);
        }
      }

      const validTimes = times.filter(t => t !== null);
      if (validTimes.length > 0) {
        const avgTime = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
        const p95Time = validTimes.sort((a, b) => a - b)[Math.floor(validTimes.length * 0.95)];

        this.results.marketplace[test.name] = {
          target: test.target,
          average: Math.round(avgTime),
          p95: Math.round(p95Time),
          iterations: validTimes.length,
          status: avgTime <= test.target ? 'PASS' : 'FAIL'
        };

        console.log(`   Target: <${test.target}ms`);
        console.log(`   Average: ${Math.round(avgTime)}ms`);
        console.log(`   Status: ${avgTime <= test.target ? '✅ PASS' : '❌ FAIL'}`);
      }
    }
  }

  async validateLinkTracking() {
    console.log('\n🔗 Validating Link Tracking (Target: <1ms response)');
    console.log('--------------------------------------------------');
    
    const targetTime = 1; // ms
    const iterations = 100;
    
    try {
      console.log(`\n⏱️  Testing ${iterations} link redirects...`);
      
      // First create a test link
      const createResponse = await axios.post(`${this.services.linkTracking}/api/v1/links`, {
        target_url: 'https://example.com',
        title: 'Performance Test Link',
        tenant_id: 'performance_test'
      });
      
      if (createResponse.status !== 201) {
        throw new Error('Failed to create test link');
      }
      
      const linkCode = createResponse.data.data.short_code;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        try {
          // Test redirect performance (don't follow redirects)
          await axios.get(`${this.services.linkTracking}/${linkCode}`, {
            maxRedirects: 0,
            validateStatus: (status) => status === 302
          });
          
          const endTime = performance.now();
          times.push(endTime - startTime);
        } catch (error) {
          if (error.response && error.response.status === 302) {
            const endTime = performance.now();
            times.push(endTime - startTime);
          } else {
            times.push(null);
          }
        }
      }
      
      const validTimes = times.filter(t => t !== null);
      if (validTimes.length > 0) {
        const avgTime = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
        const minTime = Math.min(...validTimes);
        const maxTime = Math.max(...validTimes);
        const p95Time = validTimes.sort((a, b) => a - b)[Math.floor(validTimes.length * 0.95)];
        
        this.results.linkTracking = {
          target: targetTime,
          average: Math.round(avgTime * 100) / 100,
          min: Math.round(minTime * 100) / 100,
          max: Math.round(maxTime * 100) / 100,
          p95: Math.round(p95Time * 100) / 100,
          iterations: validTimes.length,
          status: avgTime <= targetTime ? 'PASS' : 'FAIL'
        };
        
        console.log(`   Target: <${targetTime}ms`);
        console.log(`   Average: ${Math.round(avgTime * 100) / 100}ms`);
        console.log(`   P95: ${Math.round(p95Time * 100) / 100}ms`);
        console.log(`   Status: ${avgTime <= targetTime ? '✅ PASS' : '❌ FAIL'}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      this.results.linkTracking = {
        error: error.message,
        status: 'ERROR'
      };
    }
  }

  async validateCompetitiveAdvantage() {
    console.log('\n🏆 Validating Competitive Advantage Claims (97-98% better)');
    console.log('----------------------------------------------------------');

    // Industry benchmarks for comparison
    const industryBenchmarks = {
      queryResponseTime: 250, // ms (Google Analytics average)
      eventProcessingRate: 1000, // events/sec (industry average)
      mlInferenceLatency: 500, // ms (typical ML API)
      linkRedirectTime: 50 // ms (typical redirect service)
    };

    const competitiveAnalysis = {};

    // Query Performance Advantage
    const queryResults = Object.values(this.results.queryPerformance).filter(r => r.status !== 'ERROR');
    if (queryResults.length > 0) {
      const avgQueryTime = queryResults.reduce((sum, r) => sum + r.average, 0) / queryResults.length;
      const improvement = ((industryBenchmarks.queryResponseTime - avgQueryTime) / industryBenchmarks.queryResponseTime) * 100;

      competitiveAnalysis.queryPerformance = {
        ourPerformance: Math.round(avgQueryTime * 100) / 100,
        industryBenchmark: industryBenchmarks.queryResponseTime,
        improvementPercentage: Math.round(improvement),
        claimedImprovement: 97,
        status: improvement >= 90 ? 'VALIDATED' : 'NEEDS_REVIEW'
      };

      console.log(`\n🔍 Query Performance Advantage:`);
      console.log(`   Our Performance: ${Math.round(avgQueryTime * 100) / 100}ms`);
      console.log(`   Industry Benchmark: ${industryBenchmarks.queryResponseTime}ms`);
      console.log(`   Actual Improvement: ${Math.round(improvement)}%`);
      console.log(`   Claimed Improvement: 97%`);
      console.log(`   Status: ${improvement >= 90 ? '✅ VALIDATED' : '⚠️  NEEDS REVIEW'}`);
    }

    // Event Processing Advantage
    const eventResults = Object.values(this.results.eventProcessing).filter(r => r.status !== 'ERROR');
    if (eventResults.length > 0) {
      const avgEventRate = eventResults.reduce((sum, r) => sum + r.actualRate, 0) / eventResults.length;
      const improvement = ((avgEventRate - industryBenchmarks.eventProcessingRate) / industryBenchmarks.eventProcessingRate) * 100;

      competitiveAnalysis.eventProcessing = {
        ourPerformance: Math.round(avgEventRate),
        industryBenchmark: industryBenchmarks.eventProcessingRate,
        improvementPercentage: Math.round(improvement),
        claimedImprovement: 2400, // 24x improvement
        status: improvement >= 2000 ? 'VALIDATED' : 'NEEDS_REVIEW'
      };

      console.log(`\n📊 Event Processing Advantage:`);
      console.log(`   Our Performance: ${Math.round(avgEventRate)} events/sec`);
      console.log(`   Industry Benchmark: ${industryBenchmarks.eventProcessingRate} events/sec`);
      console.log(`   Actual Improvement: ${Math.round(improvement)}%`);
      console.log(`   Status: ${improvement >= 2000 ? '✅ VALIDATED' : '⚠️  NEEDS REVIEW'}`);
    }

    this.results.competitiveAnalysis = competitiveAnalysis;
  }

  generateTestEvents(count) {
    const events = [];
    const eventTypes = ['page_view', 'purchase', 'signup', 'login', 'add_to_cart'];
    
    for (let i = 0; i < count; i++) {
      events.push({
        event_id: `test_event_${i}`,
        user_id: `test_user_${Math.floor(Math.random() * 1000)}`,
        event_type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
        timestamp: new Date().toISOString(),
        properties: {
          page: '/test',
          source: 'performance_test',
          value: Math.random() * 100
        },
        revenue: Math.random() > 0.8 ? Math.random() * 500 : null
      });
    }
    
    return events;
  }

  generateReport() {
    console.log('\n📋 Performance Validation Report');
    console.log('================================');
    
    console.log('\n🎯 Claimed vs Actual Performance:');
    console.log('----------------------------------');
    
    // Event Processing Summary
    const eventResults = Object.values(this.results.eventProcessing).filter(r => r.status !== 'ERROR');
    if (eventResults.length > 0) {
      const avgAchievement = eventResults.reduce((sum, r) => sum + r.achievementPercentage, 0) / eventResults.length;
      console.log(`📊 Event Processing: ${Math.round(avgAchievement)}% of claimed 24,390 events/sec`);
    }
    
    // Query Performance Summary
    const queryResults = Object.values(this.results.queryPerformance).filter(r => r.status !== 'ERROR');
    if (queryResults.length > 0) {
      const passedQueries = queryResults.filter(r => r.status === 'PASS').length;
      console.log(`🔍 Query Performance: ${passedQueries}/${queryResults.length} queries meet 6-11ms target`);
    }
    
    // ML Inference Summary
    if (this.results.mlInference.status !== 'ERROR') {
      console.log(`🤖 ML Inference: ${this.results.mlInference.achievementPercentage}% of claimed 343.52 predictions/sec`);
    }
    
    // Link Tracking Summary
    if (this.results.linkTracking.status !== 'ERROR') {
      const status = this.results.linkTracking.status === 'PASS' ? 'MEETS' : 'EXCEEDS';
      console.log(`🔗 Link Tracking: ${status} <1ms target (avg: ${this.results.linkTracking.average}ms)`);
    }
  }

  identifyGaps() {
    console.log('\n⚠️  Performance Gaps Identified:');
    console.log('--------------------------------');
    
    // Check event processing gaps
    const eventResults = Object.values(this.results.eventProcessing).filter(r => r.status !== 'ERROR');
    const failedEventTests = eventResults.filter(r => r.status === 'FAIL');
    if (failedEventTests.length > 0) {
      this.results.gaps.push({
        area: 'Event Processing',
        issue: `${failedEventTests.length} tests failed to meet 24,390 events/sec target`,
        impact: 'High - Core performance claim not validated'
      });
    }
    
    // Check query performance gaps
    const queryResults = Object.values(this.results.queryPerformance).filter(r => r.status !== 'ERROR');
    const failedQueryTests = queryResults.filter(r => r.status === 'FAIL');
    if (failedQueryTests.length > 0) {
      this.results.gaps.push({
        area: 'Query Performance',
        issue: `${failedQueryTests.length} query types exceed 6-11ms target`,
        impact: 'Medium - Query performance claims need adjustment'
      });
    }
    
    // Display gaps
    if (this.results.gaps.length === 0) {
      console.log('✅ No significant performance gaps identified');
    } else {
      this.results.gaps.forEach((gap, index) => {
        console.log(`${index + 1}. ${gap.area}: ${gap.issue}`);
        console.log(`   Impact: ${gap.impact}`);
      });
    }
  }

  generateRecommendations() {
    console.log('\n💡 Performance Optimization Recommendations:');
    console.log('--------------------------------------------');

    let recommendationCount = 0;

    // Event Processing Recommendations
    const eventResults = Object.values(this.results.eventProcessing).filter(r => r.status !== 'ERROR');
    const failedEventTests = eventResults.filter(r => r.status === 'FAIL');
    if (failedEventTests.length > 0) {
      recommendationCount++;
      console.log(`${recommendationCount}. Event Processing Optimization:`);
      console.log('   - Implement batch processing with larger batch sizes');
      console.log('   - Add Redis caching for event deduplication');
      console.log('   - Consider horizontal scaling of Analytics Service');
      console.log('   - Optimize database connection pooling');

      this.results.recommendations.push({
        area: 'Event Processing',
        priority: 'High',
        actions: [
          'Implement batch processing with larger batch sizes',
          'Add Redis caching for event deduplication',
          'Consider horizontal scaling of Analytics Service',
          'Optimize database connection pooling'
        ]
      });
    }

    // Query Performance Recommendations
    const queryResults = Object.values(this.results.queryPerformance).filter(r => r.status !== 'ERROR');
    const slowQueries = queryResults.filter(r => r.status === 'FAIL');
    if (slowQueries.length > 0) {
      recommendationCount++;
      console.log(`${recommendationCount}. Query Performance Optimization:`);
      console.log('   - Add database indexes for frequently queried columns');
      console.log('   - Implement query result caching with Redis');
      console.log('   - Consider TimescaleDB continuous aggregates');
      console.log('   - Optimize query execution plans');

      this.results.recommendations.push({
        area: 'Query Performance',
        priority: 'Medium',
        actions: [
          'Add database indexes for frequently queried columns',
          'Implement query result caching with Redis',
          'Consider TimescaleDB continuous aggregates',
          'Optimize query execution plans'
        ]
      });
    }

    // ML Inference Recommendations
    if (this.results.mlInference.status === 'FAIL') {
      recommendationCount++;
      console.log(`${recommendationCount}. ML Inference Optimization:`);
      console.log('   - Implement model caching and batch inference');
      console.log('   - Consider GPU acceleration for ML workloads');
      console.log('   - Optimize feature extraction pipeline');
      console.log('   - Add model result caching');

      this.results.recommendations.push({
        area: 'ML Inference',
        priority: 'Medium',
        actions: [
          'Implement model caching and batch inference',
          'Consider GPU acceleration for ML workloads',
          'Optimize feature extraction pipeline',
          'Add model result caching'
        ]
      });
    }

    // Marketplace Performance Recommendations
    const marketplaceResults = Object.values(this.results.marketplace).filter(r => r.status !== 'ERROR');
    const slowMarketplace = marketplaceResults.filter(r => r.status === 'FAIL');
    if (slowMarketplace.length > 0) {
      recommendationCount++;
      console.log(`${recommendationCount}. Marketplace Performance Optimization:`);
      console.log('   - Implement partner data caching');
      console.log('   - Optimize compatibility scoring algorithms');
      console.log('   - Add background processing for complex calculations');
      console.log('   - Consider CDN for marketplace assets');

      this.results.recommendations.push({
        area: 'Marketplace Performance',
        priority: 'Low',
        actions: [
          'Implement partner data caching',
          'Optimize compatibility scoring algorithms',
          'Add background processing for complex calculations',
          'Consider CDN for marketplace assets'
        ]
      });
    }

    if (recommendationCount === 0) {
      console.log('✅ No performance optimizations needed - all targets met!');
    }
  }

  async cleanup() {
    await this.db.end();
    this.redis.disconnect();
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new ComprehensivePerformanceValidator();
  validator.validateAll().catch(console.error);
}

module.exports = ComprehensivePerformanceValidator;
