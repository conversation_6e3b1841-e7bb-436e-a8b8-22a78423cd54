{"name": "ecommerce-analytics-performance-tests", "version": "1.0.0", "description": "Performance testing suite for E-commerce Analytics SaaS platform", "main": "index.js", "scripts": {"test": "npm run test:all", "test:all": "npm run test:api && npm run test:load && npm run test:stress", "test:api": "k6 run tests/api-performance.js", "test:load": "k6 run tests/load-test.js", "test:stress": "k6 run tests/stress-test.js", "test:spike": "k6 run tests/spike-test.js", "test:endurance": "k6 run tests/endurance-test.js", "test:analytics": "k6 run tests/analytics-performance.js", "test:billing": "k6 run tests/billing-performance.js", "test:integration": "k6 run tests/integration-performance.js", "test:dashboard": "k6 run tests/dashboard-performance.js", "test:database": "node tests/database-performance.js", "test:redis": "node tests/redis-performance.js", "validate:comprehensive": "node comprehensive-performance-validation.js", "validate:claims": "node validate-performance-claims.js", "validate:marketplace": "node validate-marketplace-performance.js", "benchmark": "node benchmark/run-benchmarks.js", "monitor": "node monitoring/performance-monitor.js", "report": "node reports/generate-report.js", "setup": "node setup/test-data-setup.js", "cleanup": "node setup/test-data-cleanup.js", "profile": "clinic doctor --autocannon [ -c 100 -d 30 ] -- node ../services/analytics/src/index.js", "flame": "clinic flame --autocannon [ -c 100 -d 30 ] -- node ../services/analytics/src/index.js", "bubbleprof": "clinic bubbleprof --autocannon [ -c 100 -d 30 ] -- node ../services/analytics/src/index.js"}, "dependencies": {"k6": "^0.47.0", "autocannon": "^7.12.0", "clinic": "^12.0.0", "artillery": "^2.0.0", "loadtest": "^8.0.2", "pg": "^8.11.0", "redis": "^4.6.7", "axios": "^1.4.0", "ws": "^8.13.0", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "chalk": "^4.1.2", "ora": "^5.4.1", "table": "^6.8.1", "json2csv": "^6.1.0", "html-report-generator": "^1.1.0", "prometheus-api-metrics": "^3.2.2", "influx": "^5.9.3"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.42.0", "nodemon": "^2.0.22"}, "keywords": ["performance", "load-testing", "stress-testing", "k6", "autocannon", "clinic", "benchmarking"], "author": "E-commerce Analytics Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}