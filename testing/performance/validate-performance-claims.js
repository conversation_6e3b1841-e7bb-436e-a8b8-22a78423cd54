#!/usr/bin/env node

/**
 * Performance Claims Validation Script
 * Validates specific performance claims made in sales and marketing materials
 * against actual platform capabilities and documented benchmarks
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const ComprehensivePerformanceValidator = require('./comprehensive-performance-validation');

class PerformanceClaimsValidator {
  constructor() {
    this.claims = {
      // Core performance claims from documentation
      eventProcessing: {
        claimed: 24390, // events/sec
        source: 'PERFORMANCE_BENCHMARKS.md',
        tolerance: 0.1 // 10% tolerance
      },
      queryResponse: {
        claimed: 11, // ms (upper bound)
        source: 'PERFORMANCE_BENCHMARKS.md',
        tolerance: 0.2 // 20% tolerance
      },
      mlInference: {
        claimed: 343.52, // predictions/sec
        source: 'PERFORMANCE_BENCHMARKS.md',
        tolerance: 0.15 // 15% tolerance
      },
      linkTracking: {
        claimed: 1, // ms (sub-millisecond)
        source: 'Link Tracking Service README',
        tolerance: 0.5 // 50% tolerance for sub-ms claims
      },
      competitiveAdvantage: {
        claimed: 97, // % improvement over competitors
        source: 'Sales materials',
        tolerance: 0.05 // 5% tolerance
      }
    };
    
    this.validationResults = {};
    this.discrepancies = [];
    this.validator = new ComprehensivePerformanceValidator();
  }

  async validateAllClaims() {
    console.log('🔍 Performance Claims Validation');
    console.log('================================');
    console.log('Validating claims against actual platform performance...\n');
    
    try {
      // Run comprehensive performance tests
      await this.validator.validateAll();
      
      // Validate each claim
      await this.validateEventProcessingClaim();
      await this.validateQueryResponseClaim();
      await this.validateMLInferenceClaim();
      await this.validateLinkTrackingClaim();
      await this.validateCompetitiveAdvantageClaim();
      
      // Generate validation report
      this.generateValidationReport();
      this.identifyDiscrepancies();
      this.generateRecommendations();
      
    } catch (error) {
      console.error('❌ Claims validation failed:', error);
    }
  }

  async validateEventProcessingClaim() {
    console.log('📊 Validating Event Processing Claim (24,390 events/sec)');
    console.log('--------------------------------------------------------');
    
    const claim = this.claims.eventProcessing;
    const results = this.validator.results.eventProcessing;
    
    if (Object.keys(results).length === 0) {
      this.validationResults.eventProcessing = {
        status: 'NO_DATA',
        message: 'No event processing test results available'
      };
      return;
    }
    
    // Get average actual performance
    const validResults = Object.values(results).filter(r => r.status !== 'ERROR' && r.actualRate);
    if (validResults.length === 0) {
      this.validationResults.eventProcessing = {
        status: 'ERROR',
        message: 'All event processing tests failed'
      };
      return;
    }
    
    const avgActualRate = validResults.reduce((sum, r) => sum + r.actualRate, 0) / validResults.length;
    const achievementPercentage = (avgActualRate / claim.claimed) * 100;
    const withinTolerance = Math.abs(avgActualRate - claim.claimed) / claim.claimed <= claim.tolerance;
    
    this.validationResults.eventProcessing = {
      claimed: claim.claimed,
      actual: Math.round(avgActualRate),
      achievementPercentage: Math.round(achievementPercentage),
      withinTolerance,
      status: withinTolerance ? 'VALIDATED' : 'DISCREPANCY',
      tolerance: claim.tolerance * 100
    };
    
    console.log(`   Claimed: ${claim.claimed} events/sec`);
    console.log(`   Actual: ${Math.round(avgActualRate)} events/sec`);
    console.log(`   Achievement: ${Math.round(achievementPercentage)}%`);
    console.log(`   Status: ${withinTolerance ? '✅ VALIDATED' : '⚠️  DISCREPANCY'}`);
    
    if (!withinTolerance) {
      this.discrepancies.push({
        claim: 'Event Processing Rate',
        expected: claim.claimed,
        actual: Math.round(avgActualRate),
        impact: 'High - Core performance claim'
      });
    }
  }

  async validateQueryResponseClaim() {
    console.log('\n🔍 Validating Query Response Claim (6-11ms)');
    console.log('--------------------------------------------');
    
    const claim = this.claims.queryResponse;
    const results = this.validator.results.queryPerformance;
    
    if (Object.keys(results).length === 0) {
      this.validationResults.queryResponse = {
        status: 'NO_DATA',
        message: 'No query performance test results available'
      };
      return;
    }
    
    // Get average query response time
    const validResults = Object.values(results).filter(r => r.status !== 'ERROR' && r.average);
    if (validResults.length === 0) {
      this.validationResults.queryResponse = {
        status: 'ERROR',
        message: 'All query performance tests failed'
      };
      return;
    }
    
    const avgResponseTime = validResults.reduce((sum, r) => sum + r.average, 0) / validResults.length;
    const withinClaim = avgResponseTime <= claim.claimed;
    const withinTolerance = avgResponseTime <= claim.claimed * (1 + claim.tolerance);
    
    this.validationResults.queryResponse = {
      claimed: `<${claim.claimed}ms`,
      actual: `${Math.round(avgResponseTime * 100) / 100}ms`,
      withinClaim,
      withinTolerance,
      status: withinClaim ? 'VALIDATED' : (withinTolerance ? 'ACCEPTABLE' : 'DISCREPANCY')
    };
    
    console.log(`   Claimed: <${claim.claimed}ms`);
    console.log(`   Actual: ${Math.round(avgResponseTime * 100) / 100}ms`);
    console.log(`   Status: ${withinClaim ? '✅ VALIDATED' : (withinTolerance ? '⚠️  ACCEPTABLE' : '❌ DISCREPANCY')}`);
    
    if (!withinTolerance) {
      this.discrepancies.push({
        claim: 'Query Response Time',
        expected: `<${claim.claimed}ms`,
        actual: `${Math.round(avgResponseTime * 100) / 100}ms`,
        impact: 'Medium - Query performance claim'
      });
    }
  }

  async validateMLInferenceClaim() {
    console.log('\n🤖 Validating ML Inference Claim (343.52 predictions/sec)');
    console.log('---------------------------------------------------------');
    
    const claim = this.claims.mlInference;
    const result = this.validator.results.mlInference;
    
    if (!result || result.status === 'ERROR') {
      this.validationResults.mlInference = {
        status: 'ERROR',
        message: 'ML inference test failed or no data available'
      };
      return;
    }
    
    const actualRate = result.actualRate || 0;
    const achievementPercentage = (actualRate / claim.claimed) * 100;
    const withinTolerance = Math.abs(actualRate - claim.claimed) / claim.claimed <= claim.tolerance;
    
    this.validationResults.mlInference = {
      claimed: claim.claimed,
      actual: actualRate,
      achievementPercentage: Math.round(achievementPercentage),
      withinTolerance,
      status: withinTolerance ? 'VALIDATED' : 'DISCREPANCY'
    };
    
    console.log(`   Claimed: ${claim.claimed} predictions/sec`);
    console.log(`   Actual: ${actualRate} predictions/sec`);
    console.log(`   Achievement: ${Math.round(achievementPercentage)}%`);
    console.log(`   Status: ${withinTolerance ? '✅ VALIDATED' : '⚠️  DISCREPANCY'}`);
    
    if (!withinTolerance) {
      this.discrepancies.push({
        claim: 'ML Inference Rate',
        expected: claim.claimed,
        actual: actualRate,
        impact: 'Medium - ML performance claim'
      });
    }
  }

  async validateLinkTrackingClaim() {
    console.log('\n🔗 Validating Link Tracking Claim (<1ms)');
    console.log('-----------------------------------------');
    
    const claim = this.claims.linkTracking;
    const result = this.validator.results.linkTracking;
    
    if (!result || result.status === 'ERROR') {
      this.validationResults.linkTracking = {
        status: 'ERROR',
        message: 'Link tracking test failed or no data available'
      };
      return;
    }
    
    const actualTime = result.average || 0;
    const withinClaim = actualTime <= claim.claimed;
    const withinTolerance = actualTime <= claim.claimed * (1 + claim.tolerance);
    
    this.validationResults.linkTracking = {
      claimed: `<${claim.claimed}ms`,
      actual: `${actualTime}ms`,
      withinClaim,
      withinTolerance,
      status: withinClaim ? 'VALIDATED' : (withinTolerance ? 'ACCEPTABLE' : 'DISCREPANCY')
    };
    
    console.log(`   Claimed: <${claim.claimed}ms`);
    console.log(`   Actual: ${actualTime}ms`);
    console.log(`   Status: ${withinClaim ? '✅ VALIDATED' : (withinTolerance ? '⚠️  ACCEPTABLE' : '❌ DISCREPANCY')}`);
    
    if (!withinTolerance) {
      this.discrepancies.push({
        claim: 'Link Tracking Response Time',
        expected: `<${claim.claimed}ms`,
        actual: `${actualTime}ms`,
        impact: 'Low - Link tracking performance'
      });
    }
  }

  async validateCompetitiveAdvantageClaim() {
    console.log('\n🏆 Validating Competitive Advantage Claim (97-98% better)');
    console.log('---------------------------------------------------------');
    
    const claim = this.claims.competitiveAdvantage;
    const results = this.validator.results.competitiveAnalysis;
    
    if (!results || Object.keys(results).length === 0) {
      this.validationResults.competitiveAdvantage = {
        status: 'NO_DATA',
        message: 'No competitive analysis data available'
      };
      return;
    }
    
    // Check query performance advantage
    if (results.queryPerformance) {
      const actualImprovement = results.queryPerformance.improvementPercentage;
      const withinTolerance = Math.abs(actualImprovement - claim.claimed) / claim.claimed <= claim.tolerance;
      
      this.validationResults.competitiveAdvantage = {
        claimed: `${claim.claimed}% improvement`,
        actual: `${actualImprovement}% improvement`,
        withinTolerance,
        status: withinTolerance ? 'VALIDATED' : 'DISCREPANCY'
      };
      
      console.log(`   Claimed: ${claim.claimed}% improvement over competitors`);
      console.log(`   Actual: ${actualImprovement}% improvement`);
      console.log(`   Status: ${withinTolerance ? '✅ VALIDATED' : '⚠️  DISCREPANCY'}`);
      
      if (!withinTolerance) {
        this.discrepancies.push({
          claim: 'Competitive Advantage',
          expected: `${claim.claimed}% improvement`,
          actual: `${actualImprovement}% improvement`,
          impact: 'High - Core sales claim'
        });
      }
    }
  }

  generateValidationReport() {
    console.log('\n📋 Performance Claims Validation Report');
    console.log('=======================================');
    
    const totalClaims = Object.keys(this.validationResults).length;
    const validatedClaims = Object.values(this.validationResults).filter(r => r.status === 'VALIDATED').length;
    const acceptableClaims = Object.values(this.validationResults).filter(r => r.status === 'ACCEPTABLE').length;
    const discrepancyClaims = Object.values(this.validationResults).filter(r => r.status === 'DISCREPANCY').length;
    
    console.log(`\n📊 Summary:`);
    console.log(`   Total Claims Tested: ${totalClaims}`);
    console.log(`   Validated: ${validatedClaims} (${Math.round(validatedClaims/totalClaims*100)}%)`);
    console.log(`   Acceptable: ${acceptableClaims} (${Math.round(acceptableClaims/totalClaims*100)}%)`);
    console.log(`   Discrepancies: ${discrepancyClaims} (${Math.round(discrepancyClaims/totalClaims*100)}%)`);
    
    const overallStatus = discrepancyClaims === 0 ? 'VALIDATED' : 
                         discrepancyClaims <= totalClaims * 0.2 ? 'MOSTLY_VALIDATED' : 'NEEDS_REVIEW';
    
    console.log(`\n🎯 Overall Status: ${overallStatus}`);
  }

  identifyDiscrepancies() {
    console.log('\n⚠️  Performance Claim Discrepancies:');
    console.log('-----------------------------------');
    
    if (this.discrepancies.length === 0) {
      console.log('✅ No significant discrepancies found between claims and actual performance');
      return;
    }
    
    this.discrepancies.forEach((discrepancy, index) => {
      console.log(`${index + 1}. ${discrepancy.claim}:`);
      console.log(`   Expected: ${discrepancy.expected}`);
      console.log(`   Actual: ${discrepancy.actual}`);
      console.log(`   Impact: ${discrepancy.impact}`);
      console.log('');
    });
  }

  generateRecommendations() {
    console.log('\n💡 Recommendations for Claims Validation:');
    console.log('-----------------------------------------');
    
    if (this.discrepancies.length === 0) {
      console.log('✅ All performance claims are validated - no changes needed');
      return;
    }
    
    console.log('1. Update marketing materials to reflect actual performance metrics');
    console.log('2. Implement performance optimizations to meet claimed targets');
    console.log('3. Add performance monitoring to prevent future discrepancies');
    console.log('4. Consider adjusting claims to be more conservative and achievable');
    console.log('5. Implement automated performance validation in CI/CD pipeline');
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new PerformanceClaimsValidator();
  validator.validateAllClaims().catch(console.error);
}

module.exports = PerformanceClaimsValidator;
