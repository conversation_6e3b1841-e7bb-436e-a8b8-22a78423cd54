# E-commerce Analytics SaaS Platform - Environment Configuration
# Copy this file to .env and configure with your actual values
# Updated for Deno 2 + Fresh implementation

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
DENO_ENV=development
NODE_ENV=development
BUILD_TARGET=development
LOG_LEVEL=info

# =============================================================================
# DENO 2 SERVICE PORTS (Updated for current architecture)
# =============================================================================
# Fresh Frontend (Server-Side Rendering + Islands)
FRESH_PORT=8000
DASHBOARD_FRONTEND_PORT=8000

# Deno 2 Backend Services
DASHBOARD_BACKEND_PORT=3000
ANALYTICS_PORT=3002
INTEGRATION_PORT=3001
BILLING_PORT=3003
ADMIN_PORT=3005

# High-performance Go service
LINK_TRACKING_PORT=8080

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL + TimescaleDB)
# =============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics
DB_USER=postgres
DB_PASSWORD=your_secure_postgres_password_here
DB_SSL=false

# TimescaleDB Configuration
TIMESCALEDB_ENABLED=true
TIMESCALEDB_COMPRESSION_ENABLED=true
TIMESCALEDB_RETENTION_POLICY=90d

# Database connection pool settings (optimized for Deno 2)
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000
DB_CONNECTION_TIMEOUT=30000

# Database URL format for Deno services
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# =============================================================================
# REDIS CONFIGURATION (Caching + Real-time Features)
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_DB=0

# Redis connection settings
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_DELAY_ON_FAILURE=100
REDIS_MAX_RETRY_ATTEMPTS=3

# Redis URL format for Deno services
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# =============================================================================
# JWT AND SECURITY (Updated for Deno 2 implementation)
# =============================================================================
JWT_SECRET=your_very_secure_jwt_secret_minimum_32_characters_long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=ecommerce-analytics-saas
JWT_AUDIENCE=ecommerce-analytics-users

# API rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_BURST=200

# CORS settings (updated for Fresh frontend)
CORS_ORIGIN=http://localhost:8000,http://localhost:3000
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400

# Security headers
ENABLE_SECURITY_HEADERS=true
CONTENT_SECURITY_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' wss: ws:;"

# =============================================================================
# FRESH FRONTEND CONFIGURATION (Port 8000)
# =============================================================================
# Service URLs for Fresh API proxy routes
DASHBOARD_API_URL=http://localhost:3000
ANALYTICS_API_URL=http://localhost:3002
INTEGRATION_API_URL=http://localhost:3001
BILLING_API_URL=http://localhost:3003
ADMIN_API_URL=http://localhost:3005
LINK_TRACKING_API_URL=http://localhost:8080

# Real-time streaming configuration
SSE_RECONNECT_TIMEOUT=2000
SSE_MAX_RECONNECT_ATTEMPTS=5
SSE_HEARTBEAT_INTERVAL=30000

# =============================================================================
# MARKETPLACE ECOSYSTEM CONFIGURATION
# =============================================================================
# Partner discovery and compatibility scoring
MARKETPLACE_ML_SCORING_ENABLED=true
MARKETPLACE_COMPATIBILITY_THRESHOLD=75.0
MARKETPLACE_PARTNER_DISCOVERY_LIMIT=50

# Data products marketplace
DATA_PRODUCTS_ENABLED=true
DATA_PRODUCTS_REVENUE_SHARE=0.30
DATA_PRODUCTS_MIN_PRICE=99.00
DATA_PRODUCTS_MAX_PRICE=4999.00

# Cross-business analytics
CROSS_BUSINESS_ANALYTICS_ENABLED=true
NETWORK_INSIGHTS_CACHE_TTL=3600
INDUSTRY_BENCHMARKS_ENABLED=true

# =============================================================================
# ADVANCED ANALYTICS CONFIGURATION
# =============================================================================
# Predictive analytics and machine learning
PREDICTIVE_ANALYTICS_ENABLED=true
ML_MODEL_REFRESH_INTERVAL=24h
CHURN_PREDICTION_THRESHOLD=0.75
REVENUE_FORECASTING_HORIZON=90d

# Real-time analytics
REALTIME_ANALYTICS_ENABLED=true
REALTIME_UPDATE_INTERVAL=30s
REALTIME_BATCH_SIZE=1000
REALTIME_BUFFER_SIZE=10000

# Performance optimization
ANALYTICS_CACHE_TTL=300
COHORT_ANALYSIS_CACHE_TTL=1800
CLV_CALCULATION_CACHE_TTL=3600
FUNNEL_ANALYSIS_CACHE_TTL=900

# =============================================================================
# E-COMMERCE PLATFORM INTEGRATIONS
# =============================================================================

# Shopify Integration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_SECRET=your_shopify_secret
SHOPIFY_WEBHOOK_SECRET=your_shopify_webhook_secret
SHOPIFY_API_VERSION=2023-10
SHOPIFY_RATE_LIMIT=2

# WooCommerce Integration
WOOCOMMERCE_KEY=your_woocommerce_consumer_key
WOOCOMMERCE_SECRET=your_woocommerce_consumer_secret
WOOCOMMERCE_RATE_LIMIT=10

# eBay Integration
EBAY_CLIENT_ID=your_ebay_client_id
EBAY_CLIENT_SECRET=your_ebay_client_secret
EBAY_ENVIRONMENT=sandbox
EBAY_RATE_LIMIT=1

# BigCommerce Integration (Optional)
BIGCOMMERCE_CLIENT_ID=your_bigcommerce_client_id
BIGCOMMERCE_CLIENT_SECRET=your_bigcommerce_client_secret
BIGCOMMERCE_ACCESS_TOKEN=your_bigcommerce_access_token
BIGCOMMERCE_STORE_HASH=your_store_hash

# Magento Integration (Optional)
MAGENTO_BASE_URL=https://your-magento-store.com
MAGENTO_ACCESS_TOKEN=your_magento_access_token

# =============================================================================
# STRIPE BILLING CONFIGURATION (Billing Service)
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_API_VERSION=2023-10-16

# Subscription configuration
STRIPE_BASIC_PRICE_ID=price_basic_monthly
STRIPE_PROFESSIONAL_PRICE_ID=price_professional_monthly
STRIPE_ENTERPRISE_PRICE_ID=price_enterprise_monthly
STRIPE_STRATEGIC_PRICE_ID=price_strategic_monthly

# Billing settings
BILLING_TRIAL_PERIOD_DAYS=14
BILLING_GRACE_PERIOD_DAYS=3
BILLING_INVOICE_REMINDER_DAYS=7

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Email templates
EMAIL_TEMPLATE_WELCOME=welcome
EMAIL_TEMPLATE_INVOICE=invoice
EMAIL_TEMPLATE_TRIAL_ENDING=trial_ending
EMAIL_TEMPLATE_PARTNERSHIP_INVITE=partnership_invite

# =============================================================================
# FILE STORAGE
# =============================================================================
# Local storage paths
UPLOAD_PATH=./uploads
EXPORT_PATH=./exports
LOG_PATH=./logs

# AWS S3 (optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=your-analytics-bucket

# =============================================================================
# MONITORING AND OBSERVABILITY (Production Ready)
# =============================================================================

# Prometheus Metrics
ENABLE_METRICS=true
METRICS_PORT=9090
METRICS_PATH=/metrics
METRICS_COLLECTION_INTERVAL=15

# Grafana Configuration
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_secure_grafana_password

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_PATH=/health

# Logging Configuration
LOG_FORMAT=json
LOG_RETENTION_DAYS=30
ENABLE_REQUEST_LOGGING=true
ENABLE_QUERY_LOGGING=true
SLOW_QUERY_THRESHOLD_MS=500

# Error Tracking
SENTRY_DSN=your_sentry_dsn_url
SENTRY_ENVIRONMENT=development
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_RELEASE=1.0.0

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DISTRIBUTED_TRACING=true
TRACING_SAMPLE_RATE=0.1

# =============================================================================
# NGINX AND SSL (Production)
# =============================================================================
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# SSL Certificate paths (for production)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =============================================================================
# DEVELOPMENT TOOLS (Development only)
# =============================================================================
# These are only used when PROFILES includes dev-tools

# PgAdmin
PGADMIN_DEFAULT_EMAIL=admin@localhost
PGADMIN_DEFAULT_PASSWORD=admin

# Redis Commander
REDIS_COMMANDER_PORT=8081

# Mailcatcher
MAILCATCHER_WEB_PORT=1080
MAILCATCHER_SMTP_PORT=1025

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket

# =============================================================================
# FEATURE FLAGS (Current Implementation Status)
# =============================================================================

# Core Analytics Features (All Implemented)
ENABLE_REAL_TIME_ANALYTICS=true
ENABLE_COHORT_ANALYSIS=true
ENABLE_CLV_CALCULATIONS=true
ENABLE_FUNNEL_ANALYSIS=true
ENABLE_ATTRIBUTION_MODELING=true
ENABLE_PREDICTIVE_ANALYTICS=true

# Marketplace Features (All Implemented)
ENABLE_MARKETPLACE_ECOSYSTEM=true
ENABLE_PARTNER_DISCOVERY=true
ENABLE_DATA_PRODUCTS_MARKETPLACE=true
ENABLE_CROSS_BUSINESS_ANALYTICS=true
ENABLE_REVENUE_ATTRIBUTION=true

# Advanced Features
ENABLE_D3_VISUALIZATIONS=true
ENABLE_REAL_TIME_STREAMING=true
ENABLE_SERVER_SENT_EVENTS=true
ENABLE_ISLANDS_ARCHITECTURE=true

# Reporting and Export Features
ENABLE_REPORT_GENERATION=true
ENABLE_REPORT_SCHEDULING=true
ENABLE_DATA_EXPORT=true
ENABLE_CUSTOM_DASHBOARDS=true

# Integration Features
ENABLE_SHOPIFY_INTEGRATION=true
ENABLE_WOOCOMMERCE_INTEGRATION=true
ENABLE_EBAY_INTEGRATION=true
ENABLE_WEBHOOK_PROCESSING=true

# Security and Compliance Features
ENABLE_MULTI_TENANT_ISOLATION=true
ENABLE_ROW_LEVEL_SECURITY=true
ENABLE_GDPR_COMPLIANCE=true
ENABLE_AUDIT_LOGGING=true

# Experimental Features (Future Development)
ENABLE_A_B_TESTING=false
ENABLE_AI_INSIGHTS=false
ENABLE_MOBILE_SDK=false
ENABLE_VOICE_ANALYTICS=false