# API Integration Guide
## E-commerce Analytics SaaS Platform - Production-Ready API Reference

This guide provides **comprehensive documentation** for integrating with the **production-ready** e-commerce analytics platform APIs. All backend services are built with **Deno 2.4+** and Oak framework, delivering **exceptional performance** (6-11ms response times, 24,390 events/sec processing) with **marketplace ecosystem** capabilities, enhanced JWT authentication, and enterprise-grade multi-tenant support.

## 🔐 **Enhanced Authentication & Marketplace Permissions**

### **Production JWT Authentication**
All API endpoints require enhanced JWT authentication with marketplace permissions:

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
X-Marketplace-Access: true
```

### **Enhanced Authentication Flow**
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "marketplace_access": true
}
```

**Enhanced Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400,
    "refresh_expires_in": 604800,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "tenant_id": "tenant-uuid",
      "role": "admin",
      "subscription_tier": "enterprise",
      "marketplace_permissions": {
        "partner_discovery": true,
        "revenue_attribution": true,
        "data_sharing": true,
        "network_insights": true,
        "cross_business_analytics": true
      }
    },
    "marketplace_features": {
      "enabled": true,
      "partner_count": 5,
      "revenue_sharing_active": true,
      "compatibility_threshold": 75.0
    }
  },
  "timestamp": "2025-01-09T10:00:00Z"
}
```

### **Token Refresh**
```http
POST /api/auth/refresh
Content-Type: application/json
Authorization: Bearer <refresh_token>

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### User Registration
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "companyName": "Acme Corp"
}
```

## 🏗️ Service Architecture

### Service Endpoints
- **Dashboard Backend**: `http://localhost:3000` - API Gateway & Data Aggregation
- **Analytics Service**: `http://localhost:3002` - Customer Journey & Analytics
- **Billing Service**: `http://localhost:3003` - Subscription & Payment Management
- **Integration Service**: `http://localhost:3001` - E-commerce Platform Integrations
- **Fresh Frontend**: `http://localhost:8000` - Server-Side Rendered UI

### Multi-tenant Request Headers
All requests must include the tenant context:

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 🌟 **Marketplace Ecosystem API**

### **Partner Discovery & Compatibility**

#### **Discover Compatible Partners**
```http
GET /api/marketplace/partners/discover
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
X-Marketplace-Access: true

Query Parameters:
- industry: string (optional) - Filter by industry
- compatibility_threshold: number (default: 75) - Minimum compatibility score
- geographic_region: string (optional) - Geographic filter
- revenue_range: string (optional) - Revenue range filter
- limit: number (default: 20, max: 100) - Number of results
```

**Response:**
```json
{
  "success": true,
  "data": {
    "partners": [
      {
        "partner_id": "partner-uuid",
        "company_name": "Fashion Forward Inc",
        "industry": "fashion",
        "compatibility_score": 87.5,
        "confidence_level": 92.3,
        "geographic_alignment": 85.0,
        "seasonal_alignment": 90.2,
        "customer_overlap_potential": 15.5,
        "revenue_synergy_score": 88.7,
        "partnership_potential": {
          "estimated_revenue_increase": "15-25%",
          "customer_acquisition_potential": 1250,
          "market_expansion_opportunities": ["premium_segment", "international"]
        },
        "contact_info": {
          "contact_person": "Jane Smith",
          "email": "<EMAIL>"
        }
      }
    ],
    "total_matches": 45,
    "search_metadata": {
      "search_time_ms": 425,
      "factors_analyzed": 52,
      "ml_model_version": "v2.1.0"
    }
  },
  "performance": {
    "query_time_ms": 425,
    "cache_hit": false
  }
}
```

#### **Create Partnership Request**
```http
POST /api/marketplace/partnerships/request
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json

{
  "partner_tenant_id": "partner-uuid",
  "partnership_type": "revenue_sharing",
  "proposed_terms": {
    "commission_rate": 5.0,
    "attribution_model": "last_touch",
    "data_sharing_level": "aggregated",
    "revenue_split": {
      "referrer_percentage": 5.0,
      "platform_percentage": 1.0
    }
  },
  "message": "We'd love to explore a partnership opportunity...",
  "proposed_start_date": "2025-02-01"
}
```

### **Revenue Attribution & Analytics**

#### **Get Partnership Revenue Attribution**
```http
GET /api/marketplace/analytics/revenue-attribution
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>

Query Parameters:
- partner_tenant_id: string (optional) - Specific partner filter
- date_from: string (ISO 8601) - Start date
- date_to: string (ISO 8601) - End date
- attribution_model: string - "first_touch", "last_touch", "linear", "time_decay"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_attributed_revenue": 245750.75,
    "total_commission_paid": 12287.54,
    "partnerships": [
      {
        "partner_tenant_id": "partner-uuid",
        "partner_name": "Fashion Forward Inc",
        "attributed_revenue": 125750.50,
        "commission_rate": 5.0,
        "commission_amount": 6287.53,
        "attribution_model": "last_touch",
        "performance_trend": "increasing",
        "customer_referrals": 245,
        "conversion_rate": 12.5
      }
    ],
    "attribution_summary": {
      "direct_sales": 75.2,
      "partner_attributed": 24.8
    }
  },
  "performance": {
    "query_time_ms": 65,
    "cache_hit": true
  }
}
```

#### **Get Network Insights & Industry Benchmarks**
```http
GET /api/marketplace/analytics/network-insights
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>

Query Parameters:
- insight_type: string - "industry_benchmarks", "competitive_analysis", "market_trends"
- industry: string (optional) - Industry filter
- anonymized: boolean (default: true) - Include anonymized network data
```

**Response:**
```json
{
  "success": true,
  "data": {
    "industry_benchmarks": {
      "average_conversion_rate": 8.5,
      "average_order_value": 425.30,
      "customer_lifetime_value": 1250.75,
      "churn_rate": 15.2,
      "marketplace_participation_rate": 35.8
    },
    "your_performance": {
      "conversion_rate": 12.5,
      "percentile_rank": 85,
      "performance_vs_average": "+47%",
      "marketplace_revenue_contribution": 24.8
    },
    "market_trends": [
      {
        "trend": "Mobile commerce growth",
        "impact": "high",
        "recommendation": "Optimize mobile checkout experience",
        "opportunity_score": 8.5
      }
    ],
    "partnership_opportunities": {
      "high_potential_partners": 12,
      "estimated_revenue_uplift": "20-35%",
      "recommended_actions": ["expand_geographic_reach", "premium_partnerships"]
    }
  },
  "performance": {
    "query_time_ms": 850,
    "cache_hit": false,
    "insight_freshness": "2 hours"
  }
}
```

**Performance**: Marketplace endpoints average <500ms response time with ML-powered insights

---

## 📊 Analytics Service API (Port 3002)

### Analytics Summary
```http
GET /api/analytics/summary?tenant_id=<tenant_id>&date_from=2024-01-01&date_to=2024-01-31
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_clicks": 1250,
    "total_conversions": 89,
    "total_links": 45,
    "total_revenue": 12450.75,
    "avg_order_value": 139.90,
    "conversion_rate": 7.12,
    "period": {
      "from": "2024-01-01",
      "to": "2024-01-31"
    }
  }
}
```

### Cohort Analysis
```http
GET /api/analytics/cohorts?tenant_id=<tenant_id>&cohort_type=revenue&date_range=30d
```

**Response:**
```json
{
  "success": true,
  "data": {
    "cohorts": [
      {
        "period": "2024-01",
        "customers": 150,
        "revenue": 15000,
        "retention_rate": 85.5
      }
    ]
  }
}
```

### Attribution Analysis
```http
GET /api/analytics/attribution?tenant_id=<tenant_id>&model=last_touch&date_from=2024-01-01&date_to=2024-01-31
```

**Response:**
```json
{
  "success": true,
  "data": {
    "model": "last_touch",
    "channels": [
      {
        "channel": "organic_search",
        "conversions": 45,
        "revenue": 5500.00,
        "attribution_percentage": 35.2
      }
    ]
  }
}
```

## 🎛️ Dashboard Backend API (Port 3000)

### Dashboard Overview
```http
GET /api/dashboard/overview?period=30d&compare_period=true
```

**Response:**
```json
{
  "success": true,
  "data": {
    "metrics": {
      "total_revenue": 25000.00,
      "revenue_change": 15.5,
      "active_users": 1250,
      "users_change": 8.2,
      "conversion_rate": 3.45,
      "conversion_change": -2.1
    },
    "recent_activity": [
      {
        "id": "activity-1",
        "description": "New order from customer",
        "time": "2024-01-15T10:30:00Z",
        "type": "conversion"
      }
    ]
  }
}
```

### Real-time Metrics
```http
GET /api/dashboard/metrics
```

**Response:**
```json
{
  "success": true,
  "data": {
    "live_visitors": 45,
    "clicks_today": 1250,
    "conversions_today": 89,
    "revenue_today": 3500.00,
    "last_updated": "2024-01-15T10:30:00Z"
  }
}
```

### Top Performing Links
```http
GET /api/dashboard/top-links?metric=clicks&limit=5
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "link-1",
      "title": "Product Launch Campaign",
      "url": "https://example.com/product",
      "clicks": 1250,
      "conversions": 89,
      "conversion_rate": 7.12,
      "revenue": 12450.75
    }
  ]
}
```

## 💳 Billing Service API (Port 3003)

### Subscription Management
```http
GET /api/subscriptions
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "sub-uuid",
      "plan_id": "pro_monthly",
      "status": "active",
      "current_period_start": "2024-01-01T00:00:00Z",
      "current_period_end": "2024-02-01T00:00:00Z",
      "amount": 99.00,
      "currency": "USD"
    }
  ]
}
```

### Create Subscription
```http
POST /api/subscriptions
Content-Type: application/json

{
  "plan_id": "pro_monthly",
  "payment_method_id": "pm_stripe_payment_method_id"
}
```

### Process Payment
```http
POST /api/payments/process
Content-Type: application/json

{
  "amount": 9900,
  "currency": "USD",
  "payment_method_id": "pm_stripe_payment_method_id",
  "description": "Pro Plan Subscription"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "payment_intent_id": "pi_stripe_payment_intent_id",
    "status": "succeeded",
    "amount": 9900,
    "currency": "USD"
  }
}
```

### Invoice Management
```http
GET /api/invoices?page=1&limit=10&status=paid
```

**Response:**
```json
{
  "success": true,
  "data": {
    "invoices": [
      {
        "id": "inv-uuid",
        "invoice_number": "INV-2024-001",
        "amount_due": 9900,
        "currency": "USD",
        "status": "paid",
        "due_date": "2024-02-01T00:00:00Z",
        "paid_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

## 🔗 Integration Service API (Port 3001)

### Platform Integrations
```http
GET /api/integrations
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "integration-uuid",
      "platform": "shopify",
      "name": "My Shopify Store",
      "status": "active",
      "last_sync_at": "2024-01-15T10:30:00Z",
      "config": {
        "shop_domain": "mystore.myshopify.com",
        "sync_frequency": "hourly"
      }
    }
  ]
}
```

### Create Integration
```http
POST /api/integrations
Content-Type: application/json

{
  "platform": "shopify",
  "name": "My Shopify Store",
  "credentials": {
    "access_token": "shopify_access_token",
    "shop_domain": "mystore.myshopify.com"
  },
  "config": {
    "sync_frequency": "hourly",
    "sync_products": true,
    "sync_orders": true
  }
}
```

### Test Integration
```http
POST /api/integrations/:id/test
```

**Response:**
```json
{
  "success": true,
  "data": {
    "connection_status": "success",
    "api_version": "2023-10",
    "permissions": ["read_products", "read_orders"],
    "test_timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Shopify Data Sync
```http
POST /api/shopify/sync/products
Content-Type: application/json

{
  "integration_id": "integration-uuid"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "job_id": "sync-job-uuid",
    "status": "started",
    "estimated_duration": "5-10 minutes"
  }
}
```

### Sync Status
```http
GET /api/sync/status/:job_id
```

**Response:**
```json
{
  "success": true,
  "data": {
    "job_id": "sync-job-uuid",
    "status": "completed",
    "progress": 100,
    "total_items": 250,
    "processed_items": 250,
    "started_at": "2024-01-15T10:30:00Z",
    "completed_at": "2024-01-15T10:37:00Z"
  }
}
```

## 🔄 Real-time Updates

### Server-Sent Events (SSE)
The Fresh frontend supports real-time updates via Server-Sent Events:

```javascript
// Connect to real-time metrics stream
const eventSource = new EventSource('/api/realtime/metrics');

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // Update UI with real-time data
  updateMetrics(data);
};
```

### Webhook Endpoints

#### Shopify Webhooks
```http
POST /webhooks/shopify
Headers:
  X-Shopify-Hmac-Sha256: <signature>
  X-Shopify-Topic: orders/create
Content-Type: application/json

{
  "id": 12345,
  "email": "<EMAIL>",
  "total_price": "99.00",
  "currency": "USD"
}
```

#### Stripe Webhooks
```http
POST /webhooks/stripe
Headers:
  Stripe-Signature: <signature>
Content-Type: application/json

{
  "type": "invoice.payment_succeeded",
  "data": {
    "object": {
      "id": "in_stripe_invoice_id",
      "amount_paid": 9900,
      "currency": "usd"
    }
  }
}
```

## 🔍 Health & Monitoring

### Health Check Endpoints
All services provide standardized health check endpoints:

```http
GET /health          # Basic health check
GET /ready           # Readiness check (dependencies)
GET /live            # Liveness check
GET /metrics         # Prometheus metrics
```

**Health Response:**
```json
{
  "success": true,
  "service": "analytics-service",
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0"
}
```

**Readiness Response:**
```json
{
  "success": true,
  "service": "analytics-service",
  "status": "ready",
  "checks": {
    "database": "healthy",
    "redis": "healthy"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🚨 Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "correlation_id": "req-uuid"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Rate Limited
- `500` - Internal Server Error
- `503` - Service Unavailable

## 📝 Rate Limiting

All services implement per-tenant rate limiting:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## 🔧 Development Tools

### API Testing with curl
```bash
# Login and get token
TOKEN=$(curl -s -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  | jq -r '.data.token')

# Use token for authenticated requests
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3002/api/analytics/summary?tenant_id=tenant-uuid
```

### Postman Collection
A complete Postman collection is available at `/docs/postman/` with pre-configured requests for all endpoints.

## 📚 Additional Resources

- [System Architecture Guide](./SYSTEM_ARCHITECTURE.md)
- [Development Setup Guide](./DEVELOPMENT_SETUP.md)
- [Service-specific READMEs](../services/)
- [Fresh Frontend Documentation](../services/dashboard-fresh/README.md)

This API guide provides the foundation for integrating with the e-commerce analytics platform. For specific implementation details, refer to the individual service documentation.
