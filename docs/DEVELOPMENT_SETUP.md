# Development Setup Guide
## E-commerce Analytics SaaS Platform - Production-Ready Local Development

This guide provides step-by-step instructions for setting up the **complete production-ready** e-commerce analytics platform for local development. The platform consists of **5 Deno 2.4+ backend services**, **Fresh frontend with 36+ islands**, **marketplace ecosystem**, and supporting infrastructure with **exceptional performance** (24,390 events/sec, 6-11ms queries).

## 🛠️ Prerequisites

### **Required Software (Production Versions)**
- **Deno 2.4+** - [Install Latest Deno](https://deno.land/manual/getting_started/installation)
- **Docker & Docker Compose** - [Install Docker](https://docs.docker.com/get-docker/)
- **PostgreSQL 15+** with TimescaleDB - For time-series analytics
- **Redis 7+** - For caching and real-time features
- **Go 1.21+** - For high-performance link tracking service
- **Git** - Version control
- **VS Code** (recommended) - With Deno extension for optimal development experience

### **Verify Installation & Performance**
```bash
# Check Deno version (should be 2.4+)
deno --version
# Expected: deno 2.4.x (release, aarch64-apple-darwin)

# Check Docker
docker --version
docker-compose --version

# Check Go (for link tracking service)
go version
# Expected: go version go1.21.x

# Check Git
git --version

# Verify Deno permissions and performance
deno eval "console.log('Deno setup verified:', Deno.version)"

# Check available system resources for development
echo "CPU cores: $(nproc 2>/dev/null || sysctl -n hw.ncpu)"
echo "Memory: $(free -h 2>/dev/null || echo 'Use Activity Monitor on macOS')"
```

## 📥 Project Setup

### 1. Clone Repository
```bash
git clone <your-repository-url>
cd ecommerce-analytics-saas
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env  # or your preferred editor
```

### 3. **Comprehensive Environment Variables**
Configure the following in your `.env` file for **production-ready development**:

```bash
# Environment Configuration
DENO_ENV=development
NODE_ENV=development

# Database Configuration (PostgreSQL + TimescaleDB)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ecommerce_analytics
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false
DB_MAX_CONNECTIONS=20
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# TimescaleDB Configuration
TIMESCALEDB_ENABLED=true
TIMESCALEDB_COMPRESSION_ENABLED=true
TIMESCALEDB_RETENTION_POLICY=365d

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${REDIS_DB}

# Service Ports (All Deno 2.4+ Services)
ANALYTICS_PORT=3002
DASHBOARD_BACKEND_PORT=3000
DASHBOARD_FRONTEND_PORT=8000
BILLING_PORT=3003
INTEGRATION_PORT=3001
ADMIN_PORT=3005
LINK_TRACKING_PORT=8080

# Enhanced JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-256-bits
JWT_ISSUER=ecommerce-analytics-saas
JWT_AUDIENCE=ecommerce-analytics-users
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Marketplace Configuration (Development)
MARKETPLACE_ML_SCORING_ENABLED=true
MARKETPLACE_COMPATIBILITY_THRESHOLD=75.0
CROSS_BUSINESS_ANALYTICS_ENABLED=true
MARKETPLACE_COMMISSION_RATE=5.0

# Predictive Analytics Configuration
PREDICTIVE_ANALYTICS_ENABLED=true
ML_MODEL_REFRESH_INTERVAL=24h
CHURN_PREDICTION_THRESHOLD=0.75
REVENUE_FORECASTING_HORIZON=90d

# Real-time Analytics
REALTIME_ANALYTICS_ENABLED=true
REALTIME_UPDATE_INTERVAL=30s
REALTIME_BATCH_SIZE=1000

# E-commerce Platform API Keys (Development)
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_SECRET=your_shopify_secret
WOOCOMMERCE_KEY=your_woocommerce_key
WOOCOMMERCE_SECRET=your_woocommerce_secret
EBAY_CLIENT_ID=your_ebay_client_id
EBAY_CLIENT_SECRET=your_ebay_client_secret

# Stripe Configuration (Test Keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Development Logging
LOG_LEVEL=debug
LOG_FORMAT=pretty
ENABLE_QUERY_LOGGING=true
SLOW_QUERY_THRESHOLD_MS=100

# Performance Optimization (Development)
ANALYTICS_CACHE_TTL=60
COHORT_ANALYSIS_CACHE_TTL=300
CLV_CALCULATION_CACHE_TTL=600
```

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
```

## 🐳 Infrastructure Setup

### 1. Start Database and Redis
```bash
# Start PostgreSQL and Redis using Docker Compose
docker-compose up -d postgres redis

# Verify services are running
docker-compose ps
```

### 2. Database Setup
```bash
# Run database migrations
./scripts/migrate.sh

# Seed development data (optional)
./scripts/seed-data.js
```

### 3. Verify Database Connection
```bash
# Connect to PostgreSQL
docker exec -it ecommerce-postgres psql -U postgres -d ecommerce_analytics

# Check tables
\dt

# Exit PostgreSQL
\q
```

## 🚀 Service Development Setup

### Method 1: Individual Service Startup (Recommended for Development)

#### Terminal 1: Analytics Service
```bash
cd services/analytics-deno
deno cache src/main.ts
deno task dev
```

#### Terminal 2: Dashboard Backend
```bash
cd services/dashboard-deno
deno cache src/main.ts
deno task dev
```

#### Terminal 3: Fresh Frontend
```bash
cd services/dashboard-fresh
deno cache main.ts
deno task dev
```

#### Terminal 4: Billing Service
```bash
cd services/billing-deno
deno cache src/main.ts
deno task dev
```

#### Terminal 5: Integration Service
```bash
cd services/integration-deno
deno cache src/main.ts
deno task dev
```

#### Terminal 6: Link Tracking Service
```bash
cd services/link-tracking
go mod tidy
go run main.go
```

### Method 2: Automated Startup Script
```bash
# Start all services with a single command
./scripts/start-dev.sh
```

### Method 3: Docker Compose (Full Stack)
```bash
# Start entire platform with Docker
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

## 🔍 Verify Setup

### 1. Check Service Health
```bash
# Analytics Service
curl http://localhost:3002/health

# Dashboard Backend
curl http://localhost:3000/health

# Fresh Frontend
curl http://localhost:8000/api/health

# Billing Service
curl http://localhost:3003/health

# Integration Service
curl http://localhost:3001/health

# Link Tracking Service
curl http://localhost:8080/health
```

### 2. Test Authentication
```bash
# Register a test user
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User",
    "companyName": "Test Company"
  }'

# Login and get token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 3. Access Frontend
Open your browser and navigate to:
- **Fresh Frontend**: http://localhost:8000
- **Dashboard API**: http://localhost:3000
- **Analytics API**: http://localhost:3002

## 🧪 Development Workflow

### Running Tests
```bash
# Test all services
./scripts/run-complete-tests.sh

# Test individual services
cd services/analytics-deno && deno task test
cd services/dashboard-deno && deno task test
cd services/billing-deno && deno task test
cd services/integration-deno && deno task test
cd services/dashboard-fresh && deno task test
```

### Code Quality
```bash
# Format all Deno code
find services -name "*.ts" -o -name "*.tsx" | xargs deno fmt

# Lint all Deno code
find services -name "*.ts" -o -name "*.tsx" | xargs deno lint

# Type check all services
cd services/analytics-deno && deno task check
cd services/dashboard-deno && deno task check
cd services/billing-deno && deno task check
cd services/integration-deno && deno task check
```

### Database Operations
```bash
# Reset database
./scripts/reset-database.sh

# Run specific migration
./scripts/migrate.sh --target=20240115000000

# Backup database
./scripts/backup-database.sh

# Restore database
./scripts/restore-database.sh backup-file.sql
```

## 🔧 Development Tools

### VS Code Extensions
Recommended extensions for optimal development experience:
- **Deno** - Official Deno extension
- **TypeScript Importer** - Auto import for TypeScript
- **Tailwind CSS IntelliSense** - For Fresh frontend styling
- **REST Client** - For API testing
- **Docker** - Container management

### VS Code Settings
Add to your `.vscode/settings.json`:
```json
{
  "deno.enable": true,
  "deno.lint": true,
  "deno.unstable": false,
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.defaultFormatter": "denoland.vscode-deno",
  "editor.formatOnSave": true
}
```

### API Testing
Use the provided Postman collection or REST Client files:
```bash
# Install REST Client extension for VS Code
# Open files in docs/api-tests/ for quick API testing
```

## 🐛 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using a port
lsof -i :3000

# Kill process using port
kill -9 $(lsof -t -i:3000)
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
docker-compose ps postgres

# View PostgreSQL logs
docker-compose logs postgres

# Restart PostgreSQL
docker-compose restart postgres
```

#### Deno Permission Issues
```bash
# Ensure proper permissions are set
deno run --allow-net --allow-env --allow-read --allow-write src/main.ts
```

#### Fresh Build Issues
```bash
# Clear Fresh cache
cd services/dashboard-fresh
rm -rf .fresh/
deno task build
```

### Service Logs
```bash
# View logs for specific service
docker-compose logs -f analytics-service
docker-compose logs -f dashboard-service
docker-compose logs -f billing-service
```

### Performance Monitoring
```bash
# Monitor resource usage
docker stats

# Check service memory usage
ps aux | grep deno
```

## 📚 Next Steps

1. **Read the API Integration Guide**: [API_INTEGRATION_GUIDE.md](./API_INTEGRATION_GUIDE.md)
2. **Understand the Architecture**: [SYSTEM_ARCHITECTURE.md](./SYSTEM_ARCHITECTURE.md)
3. **Review Service Documentation**: Check individual service READMEs
4. **Set up E-commerce Integrations**: Configure Shopify, WooCommerce, eBay APIs
5. **Configure Stripe**: Set up test payment processing

## 🤝 Contributing

1. Create a feature branch from `main`
2. Make your changes following the coding standards
3. Run tests and ensure they pass
4. Update documentation as needed
5. Submit a pull request

For detailed contribution guidelines, see [CONTRIBUTING.md](../CONTRIBUTING.md).

## 📞 Support

If you encounter issues during setup:
1. Check the troubleshooting section above
2. Review service-specific documentation
3. Check GitHub issues for similar problems
4. Create a new issue with detailed error information

Happy coding! 🚀
