-- =====================================================
-- Migration 009: Revenue Monitoring and Alerting Schema
-- Description: Monitoring infrastructure for $335K+ ARR revenue streams
-- Dependencies: Migration 008 (data products marketplace)
-- Estimated Duration: 2-3 minutes
-- Target: <1% error rate, <100ms response times, 99.9% uptime
-- =====================================================

BEGIN;

-- =====================================================
-- 1. REVENUE METRICS TRACKING
-- =====================================================

-- Real-time revenue metrics storage
CREATE TABLE IF NOT EXISTS revenue_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Metric identification
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('transaction_fee', 'data_product', 'premium_matching', 'volume_discount')),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    
    -- Context
    tenant_id UUID,
    processing_time_ms INTEGER NOT NULL CHECK (processing_time_ms >= 0),
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    
    -- Metadata
    metadata JSONB,
    
    -- Constraints
    CONSTRAINT valid_metric_value CHECK (metric_value >= 0)
);

-- Convert to hypertable for time-series optimization
SELECT create_hypertable('revenue_metrics', 'timestamp', if_not_exists => TRUE);

-- Indexes for revenue metrics
CREATE INDEX IF NOT EXISTS idx_revenue_metrics_type_name ON revenue_metrics(metric_type, metric_name);
CREATE INDEX IF NOT EXISTS idx_revenue_metrics_tenant ON revenue_metrics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_revenue_metrics_success ON revenue_metrics(success);
CREATE INDEX IF NOT EXISTS idx_revenue_metrics_processing_time ON revenue_metrics(processing_time_ms);
CREATE INDEX IF NOT EXISTS idx_revenue_metrics_timestamp ON revenue_metrics(timestamp);

-- =====================================================
-- 2. REVENUE ALERTS SYSTEM
-- =====================================================

-- Revenue monitoring alerts
CREATE TABLE IF NOT EXISTS revenue_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Alert details
    alert_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('warning', 'critical')),
    message TEXT NOT NULL,
    
    -- Metric context
    metric_value DECIMAL(15,4) NOT NULL,
    threshold_value DECIMAL(15,4) NOT NULL,
    tenant_id UUID,
    
    -- Lifecycle
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    resolved_at TIMESTAMPTZ,
    
    -- Metadata
    metadata JSONB,
    
    -- Constraints
    CONSTRAINT valid_resolution_time CHECK (resolved_at IS NULL OR resolved_at >= created_at)
);

-- Indexes for alerts
CREATE INDEX IF NOT EXISTS idx_revenue_alerts_type ON revenue_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_revenue_alerts_severity ON revenue_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_revenue_alerts_tenant ON revenue_alerts(tenant_id);
CREATE INDEX IF NOT EXISTS idx_revenue_alerts_created_at ON revenue_alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_revenue_alerts_resolved ON revenue_alerts(resolved_at);
CREATE INDEX IF NOT EXISTS idx_revenue_alerts_active ON revenue_alerts(created_at) WHERE resolved_at IS NULL;

-- =====================================================
-- 3. PERFORMANCE MONITORING VIEWS
-- =====================================================

-- Real-time performance dashboard view
CREATE OR REPLACE VIEW revenue_performance_dashboard AS
SELECT 
    metric_type,
    metric_name,
    COUNT(*) as total_requests,
    COUNT(*) FILTER (WHERE success = true) as successful_requests,
    ROUND((COUNT(*) FILTER (WHERE success = true)::DECIMAL / COUNT(*)) * 100, 2) as success_rate_percentage,
    ROUND(AVG(processing_time_ms), 2) as avg_processing_time_ms,
    ROUND(MAX(processing_time_ms), 2) as max_processing_time_ms,
    ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY processing_time_ms), 2) as p95_processing_time_ms,
    ROUND(AVG(metric_value), 2) as avg_metric_value,
    COUNT(*) FILTER (WHERE timestamp >= NOW() - INTERVAL '1 hour') as requests_last_hour,
    COUNT(*) FILTER (WHERE timestamp >= NOW() - INTERVAL '24 hours') as requests_last_24h
FROM revenue_metrics
WHERE timestamp >= NOW() - INTERVAL '7 days'
GROUP BY metric_type, metric_name
ORDER BY metric_type, metric_name;

-- Revenue trends view
CREATE OR REPLACE VIEW revenue_trends_hourly AS
SELECT 
    DATE_TRUNC('hour', timestamp) as hour,
    metric_type,
    metric_name,
    COUNT(*) as request_count,
    ROUND(AVG(processing_time_ms), 2) as avg_processing_time_ms,
    ROUND(AVG(metric_value), 2) as avg_metric_value,
    COUNT(*) FILTER (WHERE success = false) as error_count
FROM revenue_metrics
WHERE timestamp >= NOW() - INTERVAL '48 hours'
GROUP BY DATE_TRUNC('hour', timestamp), metric_type, metric_name
ORDER BY hour DESC, metric_type, metric_name;

-- Alert summary view
CREATE OR REPLACE VIEW revenue_alert_summary AS
SELECT 
    alert_type,
    severity,
    COUNT(*) as total_alerts,
    COUNT(*) FILTER (WHERE resolved_at IS NULL) as active_alerts,
    COUNT(*) FILTER (WHERE resolved_at IS NOT NULL) as resolved_alerts,
    ROUND(AVG(EXTRACT(EPOCH FROM (COALESCE(resolved_at, NOW()) - created_at))), 2) as avg_resolution_time_seconds,
    MAX(created_at) as last_alert_time
FROM revenue_alerts
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY alert_type, severity
ORDER BY total_alerts DESC;

-- =====================================================
-- 4. CONTINUOUS AGGREGATES FOR PERFORMANCE
-- =====================================================

-- Hourly revenue metrics aggregation
CREATE MATERIALIZED VIEW IF NOT EXISTS revenue_metrics_hourly
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 hour', timestamp) as bucket,
    metric_type,
    metric_name,
    tenant_id,
    COUNT(*) as request_count,
    COUNT(*) FILTER (WHERE success = true) as success_count,
    AVG(processing_time_ms) as avg_processing_time_ms,
    MAX(processing_time_ms) as max_processing_time_ms,
    AVG(metric_value) as avg_metric_value,
    SUM(metric_value) as total_metric_value
FROM revenue_metrics
GROUP BY bucket, metric_type, metric_name, tenant_id;

-- Daily revenue metrics aggregation
CREATE MATERIALIZED VIEW IF NOT EXISTS revenue_metrics_daily
WITH (timescaledb.continuous) AS
SELECT 
    time_bucket('1 day', timestamp) as bucket,
    metric_type,
    metric_name,
    tenant_id,
    COUNT(*) as request_count,
    COUNT(*) FILTER (WHERE success = true) as success_count,
    AVG(processing_time_ms) as avg_processing_time_ms,
    MAX(processing_time_ms) as max_processing_time_ms,
    AVG(metric_value) as avg_metric_value,
    SUM(metric_value) as total_metric_value
FROM revenue_metrics
GROUP BY bucket, metric_type, metric_name, tenant_id;

-- =====================================================
-- 5. AUTOMATED POLICIES AND RETENTION
-- =====================================================

-- Refresh policies for continuous aggregates
SELECT add_continuous_aggregate_policy('revenue_metrics_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour',
    if_not_exists => TRUE);

SELECT add_continuous_aggregate_policy('revenue_metrics_daily',
    start_offset => INTERVAL '2 days',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 day',
    if_not_exists => TRUE);

-- Data retention policies
SELECT add_retention_policy('revenue_metrics', INTERVAL '90 days', if_not_exists => TRUE);
SELECT add_retention_policy('revenue_alerts', INTERVAL '365 days', if_not_exists => TRUE);

-- =====================================================
-- 6. MONITORING FUNCTIONS
-- =====================================================

-- Function to get current system health
CREATE OR REPLACE FUNCTION get_revenue_system_health()
RETURNS TABLE (
    metric_type TEXT,
    avg_processing_time_ms NUMERIC,
    success_rate_percentage NUMERIC,
    requests_last_hour BIGINT,
    active_alerts BIGINT,
    health_status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rm.metric_type::TEXT,
        ROUND(AVG(rm.processing_time_ms), 2) as avg_processing_time_ms,
        ROUND((COUNT(*) FILTER (WHERE rm.success = true)::DECIMAL / COUNT(*)) * 100, 2) as success_rate_percentage,
        COUNT(*) FILTER (WHERE rm.timestamp >= NOW() - INTERVAL '1 hour') as requests_last_hour,
        COALESCE(alerts.active_count, 0) as active_alerts,
        CASE 
            WHEN ROUND((COUNT(*) FILTER (WHERE rm.success = true)::DECIMAL / COUNT(*)) * 100, 2) >= 99.0 
                 AND ROUND(AVG(rm.processing_time_ms), 2) <= 100 
                 AND COALESCE(alerts.active_count, 0) = 0
            THEN 'HEALTHY'
            WHEN ROUND((COUNT(*) FILTER (WHERE rm.success = true)::DECIMAL / COUNT(*)) * 100, 2) >= 95.0 
                 AND ROUND(AVG(rm.processing_time_ms), 2) <= 200
            THEN 'WARNING'
            ELSE 'CRITICAL'
        END as health_status
    FROM revenue_metrics rm
    LEFT JOIN (
        SELECT 
            'all'::TEXT as metric_type,
            COUNT(*) as active_count
        FROM revenue_alerts 
        WHERE resolved_at IS NULL
    ) alerts ON true
    WHERE rm.timestamp >= NOW() - INTERVAL '24 hours'
    GROUP BY rm.metric_type, alerts.active_count
    ORDER BY rm.metric_type;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate revenue stream performance
CREATE OR REPLACE FUNCTION get_revenue_stream_performance(
    p_start_date TIMESTAMPTZ DEFAULT NOW() - INTERVAL '24 hours',
    p_end_date TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE (
    revenue_stream TEXT,
    total_revenue NUMERIC,
    transaction_count BIGINT,
    avg_transaction_value NUMERIC,
    success_rate NUMERIC,
    avg_processing_time NUMERIC,
    performance_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rm.metric_type::TEXT as revenue_stream,
        ROUND(SUM(rm.metric_value), 2) as total_revenue,
        COUNT(*) as transaction_count,
        ROUND(AVG(rm.metric_value), 2) as avg_transaction_value,
        ROUND((COUNT(*) FILTER (WHERE rm.success = true)::DECIMAL / COUNT(*)) * 100, 2) as success_rate,
        ROUND(AVG(rm.processing_time_ms), 2) as avg_processing_time,
        ROUND(
            (COUNT(*) FILTER (WHERE rm.success = true)::DECIMAL / COUNT(*)) * 50 +
            GREATEST(0, 50 - (AVG(rm.processing_time_ms) / 2))
        , 2) as performance_score
    FROM revenue_metrics rm
    WHERE rm.timestamp >= p_start_date
      AND rm.timestamp <= p_end_date
      AND rm.metric_name IN ('revenue', 'commission', 'fee_amount')
    GROUP BY rm.metric_type
    ORDER BY total_revenue DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on monitoring tables
ALTER TABLE revenue_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue_alerts ENABLE ROW LEVEL SECURITY;

-- RLS policies for revenue metrics
CREATE POLICY revenue_metrics_access ON revenue_metrics
    FOR ALL USING (
        tenant_id = current_setting('app.current_tenant_id')::uuid OR
        current_setting('app.current_tenant_id') = 'system' -- Allow system access
    );

-- RLS policies for revenue alerts
CREATE POLICY revenue_alerts_access ON revenue_alerts
    FOR ALL USING (
        tenant_id = current_setting('app.current_tenant_id')::uuid OR
        current_setting('app.current_tenant_id') = 'system' -- Allow system access
    );

COMMIT;

-- =====================================================
-- MIGRATION COMPLETION STATUS
-- =====================================================
SELECT 'Migration 009: Revenue Monitoring and Alerting Schema completed successfully' as status;
