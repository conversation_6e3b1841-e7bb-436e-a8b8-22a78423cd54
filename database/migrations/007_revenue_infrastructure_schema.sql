-- =====================================================
-- Migration 007: Revenue Infrastructure Schema
-- Description: Transaction fees, revenue attribution, and billing infrastructure
-- Dependencies: Migration 006 (marketplace test data)
-- Estimated Duration: 5-7 minutes
-- Target: $295K ARR transaction fee revenue stream
-- =====================================================

BEGIN;

-- =====================================================
-- 1. TRANSACTION FEE SYSTEM
-- =====================================================

-- Transaction fee calculations and tracking
CREATE TABLE IF NOT EXISTS marketplace_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    partnership_id UUID NOT NULL REFERENCES marketplace_partnerships(id),
    source_tenant_id UUID NOT NULL,
    target_tenant_id UUID NOT NULL,
    
    -- Revenue details
    attributed_revenue DECIMAL(15,2) NOT NULL CHECK (attributed_revenue >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Fee calculation
    customer_tier VARCHAR(20) NOT NULL CHECK (customer_tier IN ('core', 'advanced', 'enterprise', 'strategic')),
    base_fee_percentage DECIMAL(5,2) NOT NULL CHECK (base_fee_percentage >= 0 AND base_fee_percentage <= 100),
    volume_discount_percentage DECIMAL(5,2) NOT NULL DEFAULT 0 CHECK (volume_discount_percentage >= 0 AND volume_discount_percentage <= 100),
    final_fee_percentage DECIMAL(5,2) NOT NULL CHECK (final_fee_percentage >= 0 AND final_fee_percentage <= 100),
    
    -- Calculated amounts
    platform_commission DECIMAL(15,2) NOT NULL CHECK (platform_commission >= 0),
    partner_payout DECIMAL(15,2) NOT NULL CHECK (partner_payout >= 0),
    
    -- Attribution details
    attribution_model VARCHAR(20) NOT NULL DEFAULT 'last_touch' CHECK (attribution_model IN ('last_touch', 'first_touch', 'linear', 'time_decay')),
    attribution_confidence DECIMAL(5,2) NOT NULL DEFAULT 100.0 CHECK (attribution_confidence >= 0 AND attribution_confidence <= 100),
    
    -- Processing status
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'calculated', 'billed', 'paid', 'disputed', 'refunded')),
    processing_time_ms INTEGER,
    
    -- Audit trail
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    calculated_at TIMESTAMPTZ,
    billed_at TIMESTAMPTZ,
    paid_at TIMESTAMPTZ,
    
    -- Metadata
    transaction_metadata JSONB,
    
    -- Constraints
    CONSTRAINT valid_revenue_split CHECK (platform_commission + partner_payout = attributed_revenue)
);

-- Indexes for transaction fee system
CREATE INDEX IF NOT EXISTS idx_transactions_partnership ON marketplace_transactions(partnership_id);
CREATE INDEX IF NOT EXISTS idx_transactions_source_tenant ON marketplace_transactions(source_tenant_id);
CREATE INDEX IF NOT EXISTS idx_transactions_target_tenant ON marketplace_transactions(target_tenant_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON marketplace_transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON marketplace_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_tier_revenue ON marketplace_transactions(customer_tier, attributed_revenue);

-- =====================================================
-- 2. REVENUE ATTRIBUTION ENGINE
-- =====================================================

-- Revenue attribution tracking for cross-business events
CREATE TABLE IF NOT EXISTS revenue_attributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Event tracking
    event_id UUID NOT NULL, -- References cross_business_events
    partnership_id UUID NOT NULL REFERENCES marketplace_partnerships(id),
    customer_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    
    -- Attribution details
    touchpoint_sequence INTEGER NOT NULL CHECK (touchpoint_sequence > 0),
    attribution_weight DECIMAL(5,4) NOT NULL CHECK (attribution_weight >= 0 AND attribution_weight <= 1),
    attribution_model VARCHAR(20) NOT NULL CHECK (attribution_model IN ('last_touch', 'first_touch', 'linear', 'time_decay')),
    
    -- Revenue details
    total_revenue DECIMAL(15,2) NOT NULL CHECK (total_revenue >= 0),
    attributed_revenue DECIMAL(15,2) NOT NULL CHECK (attributed_revenue >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Timing
    event_timestamp TIMESTAMPTZ NOT NULL,
    conversion_timestamp TIMESTAMPTZ NOT NULL,
    attribution_window_hours INTEGER NOT NULL DEFAULT 168, -- 7 days default
    
    -- Tenant context
    source_tenant_id UUID NOT NULL,
    target_tenant_id UUID NOT NULL,
    
    -- Processing
    calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processing_time_ms INTEGER,
    
    -- Metadata
    attribution_metadata JSONB,
    
    -- Constraints
    CONSTRAINT valid_attribution_weight CHECK (attributed_revenue <= total_revenue),
    CONSTRAINT valid_attribution_window CHECK (conversion_timestamp >= event_timestamp),
    CONSTRAINT valid_attribution_timing CHECK (conversion_timestamp <= event_timestamp + (attribution_window_hours * INTERVAL '1 hour'))
);

-- Indexes for revenue attribution
CREATE INDEX IF NOT EXISTS idx_attributions_event ON revenue_attributions(event_id);
CREATE INDEX IF NOT EXISTS idx_attributions_partnership ON revenue_attributions(partnership_id);
CREATE INDEX IF NOT EXISTS idx_attributions_customer ON revenue_attributions(customer_id);
CREATE INDEX IF NOT EXISTS idx_attributions_session ON revenue_attributions(session_id);
CREATE INDEX IF NOT EXISTS idx_attributions_tenant_source ON revenue_attributions(source_tenant_id);
CREATE INDEX IF NOT EXISTS idx_attributions_tenant_target ON revenue_attributions(target_tenant_id);
CREATE INDEX IF NOT EXISTS idx_attributions_conversion_time ON revenue_attributions(conversion_timestamp);
CREATE INDEX IF NOT EXISTS idx_attributions_model_weight ON revenue_attributions(attribution_model, attribution_weight);

-- =====================================================
-- 3. BILLING INTEGRATION SYSTEM
-- =====================================================

-- Billing events for marketplace transactions
CREATE TABLE IF NOT EXISTS marketplace_billing_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Transaction reference
    transaction_id UUID NOT NULL REFERENCES marketplace_transactions(id),
    tenant_id UUID NOT NULL,
    
    -- Billing details
    billing_type VARCHAR(20) NOT NULL CHECK (billing_type IN ('commission', 'subscription', 'usage', 'refund')),
    amount DECIMAL(15,2) NOT NULL,
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Invoice details
    invoice_id VARCHAR(255),
    invoice_line_item_id VARCHAR(255),
    billing_period_start TIMESTAMPTZ,
    billing_period_end TIMESTAMPTZ,
    
    -- Payment processing
    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'paid', 'failed', 'disputed', 'refunded')),
    payment_method VARCHAR(50),
    payment_processor VARCHAR(50),
    payment_reference VARCHAR(255),
    
    -- Timing
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    due_date TIMESTAMPTZ,
    paid_at TIMESTAMPTZ,
    
    -- Metadata
    billing_metadata JSONB,
    
    -- Processing metrics
    processing_time_ms INTEGER
);

-- Indexes for billing events
CREATE INDEX IF NOT EXISTS idx_billing_transaction ON marketplace_billing_events(transaction_id);
CREATE INDEX IF NOT EXISTS idx_billing_tenant ON marketplace_billing_events(tenant_id);
CREATE INDEX IF NOT EXISTS idx_billing_status ON marketplace_billing_events(payment_status);
CREATE INDEX IF NOT EXISTS idx_billing_type ON marketplace_billing_events(billing_type);
CREATE INDEX IF NOT EXISTS idx_billing_created_at ON marketplace_billing_events(created_at);
CREATE INDEX IF NOT EXISTS idx_billing_due_date ON marketplace_billing_events(due_date);

-- =====================================================
-- 4. VOLUME DISCOUNT SYSTEM
-- =====================================================

-- Volume discount tracking and calculation
CREATE TABLE IF NOT EXISTS marketplace_volume_discounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Tenant and period
    tenant_id UUID NOT NULL,
    billing_period_start TIMESTAMPTZ NOT NULL,
    billing_period_end TIMESTAMPTZ NOT NULL,
    
    -- Volume metrics
    total_transaction_volume DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (total_transaction_volume >= 0),
    transaction_count INTEGER NOT NULL DEFAULT 0 CHECK (transaction_count >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Discount calculation
    applicable_tier VARCHAR(20) NOT NULL CHECK (applicable_tier IN ('advanced', 'enterprise', 'strategic')),
    volume_threshold DECIMAL(15,2) NOT NULL CHECK (volume_threshold >= 0),
    discount_percentage DECIMAL(5,2) NOT NULL CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (discount_amount >= 0),
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'applied', 'expired')),
    applied_at TIMESTAMPTZ,
    
    -- Audit
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadata
    discount_metadata JSONB,
    
    -- Constraints
    CONSTRAINT valid_billing_period CHECK (billing_period_end > billing_period_start),
    CONSTRAINT unique_tenant_period UNIQUE (tenant_id, billing_period_start, billing_period_end)
);

-- Indexes for volume discounts
CREATE INDEX IF NOT EXISTS idx_volume_discounts_tenant ON marketplace_volume_discounts(tenant_id);
CREATE INDEX IF NOT EXISTS idx_volume_discounts_period ON marketplace_volume_discounts(billing_period_start, billing_period_end);
CREATE INDEX IF NOT EXISTS idx_volume_discounts_tier ON marketplace_volume_discounts(applicable_tier);
CREATE INDEX IF NOT EXISTS idx_volume_discounts_status ON marketplace_volume_discounts(status);
CREATE INDEX IF NOT EXISTS idx_volume_discounts_volume ON marketplace_volume_discounts(total_transaction_volume);

-- =====================================================
-- 5. TIER-BASED ACCESS CONTROL SYSTEM
-- =====================================================

-- Customer tier definitions and feature access
CREATE TABLE IF NOT EXISTS marketplace_tier_features (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Tier definition
    tier_name VARCHAR(20) NOT NULL CHECK (tier_name IN ('core', 'advanced', 'enterprise', 'strategic')),
    feature_name VARCHAR(100) NOT NULL,
    feature_category VARCHAR(50) NOT NULL,
    
    -- Access control
    is_enabled BOOLEAN NOT NULL DEFAULT false,
    usage_limit INTEGER, -- NULL = unlimited
    rate_limit_per_hour INTEGER,
    
    -- Pricing
    base_price_monthly DECIMAL(10,2),
    overage_price_per_unit DECIMAL(10,4),
    
    -- Feature metadata
    feature_description TEXT,
    feature_metadata JSONB,
    
    -- Audit
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_tier_feature UNIQUE (tier_name, feature_name)
);

-- Indexes for tier features
CREATE INDEX IF NOT EXISTS idx_tier_features_tier ON marketplace_tier_features(tier_name);
CREATE INDEX IF NOT EXISTS idx_tier_features_feature ON marketplace_tier_features(feature_name);
CREATE INDEX IF NOT EXISTS idx_tier_features_category ON marketplace_tier_features(feature_category);
CREATE INDEX IF NOT EXISTS idx_tier_features_enabled ON marketplace_tier_features(is_enabled);

-- =====================================================
-- 6. PERFORMANCE OPTIMIZATION
-- =====================================================

-- Enable Row Level Security on new tables
ALTER TABLE marketplace_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue_attributions ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_billing_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_volume_discounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_tier_features ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for multi-tenant isolation
CREATE POLICY marketplace_transactions_tenant_access ON marketplace_transactions
    FOR ALL USING (
        source_tenant_id = current_setting('app.current_tenant_id')::uuid OR
        target_tenant_id = current_setting('app.current_tenant_id')::uuid
    );

CREATE POLICY revenue_attributions_tenant_access ON revenue_attributions
    FOR ALL USING (
        source_tenant_id = current_setting('app.current_tenant_id')::uuid OR
        target_tenant_id = current_setting('app.current_tenant_id')::uuid
    );

CREATE POLICY marketplace_billing_events_tenant_access ON marketplace_billing_events
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY marketplace_volume_discounts_tenant_access ON marketplace_volume_discounts
    FOR ALL USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

-- Tier features are global (no RLS needed)
CREATE POLICY marketplace_tier_features_read_all ON marketplace_tier_features
    FOR SELECT USING (true);

COMMIT;

-- =====================================================
-- MIGRATION COMPLETION STATUS
-- =====================================================
SELECT 'Migration 007: Revenue Infrastructure Schema completed successfully' as status;
