-- =====================================================
-- Migration 008: Data Products Marketplace Schema
-- Description: Data product catalog, subscriptions, and revenue sharing
-- Dependencies: Migration 007 (revenue infrastructure)
-- Estimated Duration: 3-5 minutes
-- Target: $175K ARR data products revenue stream
-- =====================================================

BEGIN;

-- =====================================================
-- 1. DATA PRODUCT CATALOG
-- =====================================================

-- Data products available in the marketplace
CREATE TABLE IF NOT EXISTS marketplace_data_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Product details
    product_name VARCHAR(255) NOT NULL,
    product_description TEXT NOT NULL,
    product_category VARCHAR(100) NOT NULL CHECK (product_category IN ('industry_benchmarks', 'trend_analysis', 'competitive_intelligence', 'custom_analytics')),
    
    -- Creator information
    creator_tenant_id UUID NOT NULL,
    creator_tier VARCHAR(20) NOT NULL CHECK (creator_tier IN ('enterprise', 'strategic')),
    
    -- Pricing and availability
    base_price_monthly DECIMAL(10,2) NOT NULL CHECK (base_price_monthly >= 0),
    pricing_model VARCHAR(20) NOT NULL DEFAULT 'subscription' CHECK (pricing_model IN ('subscription', 'one_time', 'usage_based')),
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_featured BOOLEAN NOT NULL DEFAULT false,
    
    -- Data specifications
    data_format VARCHAR(50) NOT NULL DEFAULT 'json' CHECK (data_format IN ('json', 'csv', 'parquet', 'api')),
    update_frequency VARCHAR(50) NOT NULL DEFAULT 'daily' CHECK (update_frequency IN ('real_time', 'hourly', 'daily', 'weekly', 'monthly')),
    data_retention_days INTEGER NOT NULL DEFAULT 365 CHECK (data_retention_days > 0),
    
    -- Quality metrics
    data_quality_score DECIMAL(3,2) CHECK (data_quality_score >= 0 AND data_quality_score <= 5),
    sample_size_avg INTEGER DEFAULT 0,
    accuracy_percentage DECIMAL(5,2) DEFAULT 95.0 CHECK (accuracy_percentage >= 0 AND accuracy_percentage <= 100),
    
    -- Business metrics
    total_subscribers INTEGER NOT NULL DEFAULT 0 CHECK (total_subscribers >= 0),
    monthly_revenue DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (monthly_revenue >= 0),
    creator_rating DECIMAL(3,2) DEFAULT 4.0 CHECK (creator_rating >= 0 AND creator_rating <= 5),
    
    -- Metadata
    product_metadata JSONB,
    tags TEXT[],
    
    -- Audit trail
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    deprecated_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_publish_date CHECK (published_at IS NULL OR published_at >= created_at),
    CONSTRAINT valid_deprecation_date CHECK (deprecated_at IS NULL OR deprecated_at >= published_at)
);

-- Indexes for data products
CREATE INDEX IF NOT EXISTS idx_data_products_creator ON marketplace_data_products(creator_tenant_id);
CREATE INDEX IF NOT EXISTS idx_data_products_category ON marketplace_data_products(product_category);
CREATE INDEX IF NOT EXISTS idx_data_products_active ON marketplace_data_products(is_active);
CREATE INDEX IF NOT EXISTS idx_data_products_featured ON marketplace_data_products(is_featured);
CREATE INDEX IF NOT EXISTS idx_data_products_price ON marketplace_data_products(base_price_monthly);
CREATE INDEX IF NOT EXISTS idx_data_products_rating ON marketplace_data_products(creator_rating);
CREATE INDEX IF NOT EXISTS idx_data_products_revenue ON marketplace_data_products(monthly_revenue);
CREATE INDEX IF NOT EXISTS idx_data_products_tags ON marketplace_data_products USING GIN(tags);

-- =====================================================
-- 2. DATA PRODUCT SUBSCRIPTIONS
-- =====================================================

-- Customer subscriptions to data products
CREATE TABLE IF NOT EXISTS marketplace_data_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Subscription details
    product_id UUID NOT NULL REFERENCES marketplace_data_products(id) ON DELETE CASCADE,
    subscriber_tenant_id UUID NOT NULL,
    subscriber_tier VARCHAR(20) NOT NULL CHECK (subscriber_tier IN ('advanced', 'enterprise', 'strategic')),
    
    -- Subscription terms
    subscription_type VARCHAR(20) NOT NULL DEFAULT 'monthly' CHECK (subscription_type IN ('monthly', 'annual', 'lifetime')),
    price_paid DECIMAL(10,2) NOT NULL CHECK (price_paid >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Status and lifecycle
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'paused', 'cancelled', 'expired', 'trial')),
    trial_end_date TIMESTAMPTZ,
    
    -- Billing cycle
    billing_cycle_start TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    billing_cycle_end TIMESTAMPTZ NOT NULL,
    next_billing_date TIMESTAMPTZ,
    auto_renew BOOLEAN NOT NULL DEFAULT true,
    
    -- Usage tracking
    data_access_count INTEGER NOT NULL DEFAULT 0 CHECK (data_access_count >= 0),
    last_access_at TIMESTAMPTZ,
    total_data_downloaded_mb DECIMAL(15,2) NOT NULL DEFAULT 0 CHECK (total_data_downloaded_mb >= 0),
    
    -- Revenue sharing
    creator_share_percentage DECIMAL(5,2) NOT NULL DEFAULT 70.0 CHECK (creator_share_percentage >= 0 AND creator_share_percentage <= 100),
    platform_share_percentage DECIMAL(5,2) NOT NULL DEFAULT 30.0 CHECK (platform_share_percentage >= 0 AND platform_share_percentage <= 100),
    
    -- Audit trail
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    activated_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadata
    subscription_metadata JSONB,
    
    -- Constraints
    CONSTRAINT valid_billing_cycle CHECK (billing_cycle_end > billing_cycle_start),
    CONSTRAINT valid_revenue_split CHECK (creator_share_percentage + platform_share_percentage = 100),
    CONSTRAINT valid_trial_end CHECK (trial_end_date IS NULL OR trial_end_date > created_at),
    CONSTRAINT unique_active_subscription UNIQUE (product_id, subscriber_tenant_id, status) DEFERRABLE
);

-- Indexes for subscriptions
CREATE INDEX IF NOT EXISTS idx_subscriptions_product ON marketplace_data_subscriptions(product_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_subscriber ON marketplace_data_subscriptions(subscriber_tenant_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON marketplace_data_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_billing_cycle ON marketplace_data_subscriptions(billing_cycle_start, billing_cycle_end);
CREATE INDEX IF NOT EXISTS idx_subscriptions_next_billing ON marketplace_data_subscriptions(next_billing_date);
CREATE INDEX IF NOT EXISTS idx_subscriptions_trial_end ON marketplace_data_subscriptions(trial_end_date);

-- =====================================================
-- 3. DATA PRODUCT REVENUE TRACKING
-- =====================================================

-- Revenue tracking for data product sales
CREATE TABLE IF NOT EXISTS marketplace_data_revenue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Revenue details
    product_id UUID NOT NULL REFERENCES marketplace_data_products(id),
    subscription_id UUID NOT NULL REFERENCES marketplace_data_subscriptions(id),
    
    -- Financial details
    total_amount DECIMAL(15,2) NOT NULL CHECK (total_amount >= 0),
    creator_amount DECIMAL(15,2) NOT NULL CHECK (creator_amount >= 0),
    platform_amount DECIMAL(15,2) NOT NULL CHECK (platform_amount >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Revenue period
    revenue_period_start TIMESTAMPTZ NOT NULL,
    revenue_period_end TIMESTAMPTZ NOT NULL,
    revenue_type VARCHAR(20) NOT NULL CHECK (revenue_type IN ('subscription', 'usage', 'bonus', 'refund')),
    
    -- Payment processing
    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'paid', 'failed', 'disputed', 'refunded')),
    payment_processor VARCHAR(50),
    payment_reference VARCHAR(255),
    
    -- Payout tracking
    creator_payout_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (creator_payout_status IN ('pending', 'processing', 'paid', 'failed', 'disputed')),
    creator_payout_date TIMESTAMPTZ,
    creator_payout_reference VARCHAR(255),
    
    -- Audit trail
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Metadata
    revenue_metadata JSONB,
    
    -- Constraints
    CONSTRAINT valid_revenue_period CHECK (revenue_period_end > revenue_period_start),
    CONSTRAINT valid_revenue_split CHECK (creator_amount + platform_amount = total_amount)
);

-- Indexes for revenue tracking
CREATE INDEX IF NOT EXISTS idx_data_revenue_product ON marketplace_data_revenue(product_id);
CREATE INDEX IF NOT EXISTS idx_data_revenue_subscription ON marketplace_data_revenue(subscription_id);
CREATE INDEX IF NOT EXISTS idx_data_revenue_period ON marketplace_data_revenue(revenue_period_start, revenue_period_end);
CREATE INDEX IF NOT EXISTS idx_data_revenue_payment_status ON marketplace_data_revenue(payment_status);
CREATE INDEX IF NOT EXISTS idx_data_revenue_payout_status ON marketplace_data_revenue(creator_payout_status);
CREATE INDEX IF NOT EXISTS idx_data_revenue_created_at ON marketplace_data_revenue(created_at);

-- =====================================================
-- 4. PREMIUM MATCHING SERVICES
-- =====================================================

-- Premium partner matching requests and billing
CREATE TABLE IF NOT EXISTS marketplace_premium_matching (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Matching request details
    requester_tenant_id UUID NOT NULL,
    requester_tier VARCHAR(20) NOT NULL CHECK (requester_tier IN ('advanced', 'enterprise', 'strategic')),
    
    -- Matching criteria
    target_industry VARCHAR(100),
    target_geography VARCHAR(100),
    target_company_size VARCHAR(50) CHECK (target_company_size IN ('startup', 'small', 'medium', 'large', 'enterprise')),
    target_revenue_range VARCHAR(50),
    matching_criteria JSONB NOT NULL,
    
    -- Service details
    service_type VARCHAR(50) NOT NULL DEFAULT 'introduction' CHECK (service_type IN ('introduction', 'consultation', 'custom_matching')),
    service_price DECIMAL(10,2) NOT NULL CHECK (service_price >= 0),
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Matching results
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'matched', 'completed', 'failed', 'cancelled')),
    matched_partners_count INTEGER NOT NULL DEFAULT 0 CHECK (matched_partners_count >= 0),
    introduction_success_rate DECIMAL(5,2) DEFAULT 0 CHECK (introduction_success_rate >= 0 AND introduction_success_rate <= 100),
    
    -- Success criteria and tracking
    success_criteria JSONB,
    success_achieved BOOLEAN DEFAULT false,
    success_verified_at TIMESTAMPTZ,
    success_bonus_amount DECIMAL(10,2) DEFAULT 0 CHECK (success_bonus_amount >= 0),
    
    -- Payment and billing
    payment_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'paid', 'failed', 'refunded')),
    payment_date TIMESTAMPTZ,
    refund_amount DECIMAL(10,2) DEFAULT 0 CHECK (refund_amount >= 0),
    
    -- Performance metrics
    response_time_hours INTEGER,
    customer_satisfaction_score DECIMAL(3,2) CHECK (customer_satisfaction_score >= 0 AND customer_satisfaction_score <= 5),
    
    -- Audit trail
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    assigned_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadata
    matching_metadata JSONB
);

-- Indexes for premium matching
CREATE INDEX IF NOT EXISTS idx_premium_matching_requester ON marketplace_premium_matching(requester_tenant_id);
CREATE INDEX IF NOT EXISTS idx_premium_matching_status ON marketplace_premium_matching(status);
CREATE INDEX IF NOT EXISTS idx_premium_matching_service_type ON marketplace_premium_matching(service_type);
CREATE INDEX IF NOT EXISTS idx_premium_matching_payment_status ON marketplace_premium_matching(payment_status);
CREATE INDEX IF NOT EXISTS idx_premium_matching_success ON marketplace_premium_matching(success_achieved);
CREATE INDEX IF NOT EXISTS idx_premium_matching_created_at ON marketplace_premium_matching(created_at);

-- =====================================================
-- 5. PERFORMANCE OPTIMIZATION
-- =====================================================

-- Enable Row Level Security on new tables
ALTER TABLE marketplace_data_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_data_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_data_revenue ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_premium_matching ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for multi-tenant isolation
CREATE POLICY marketplace_data_products_access ON marketplace_data_products
    FOR ALL USING (
        creator_tenant_id = current_setting('app.current_tenant_id')::uuid OR
        is_active = true -- Allow viewing active products by all tenants
    );

CREATE POLICY marketplace_data_subscriptions_access ON marketplace_data_subscriptions
    FOR ALL USING (
        subscriber_tenant_id = current_setting('app.current_tenant_id')::uuid OR
        product_id IN (SELECT id FROM marketplace_data_products WHERE creator_tenant_id = current_setting('app.current_tenant_id')::uuid)
    );

CREATE POLICY marketplace_data_revenue_access ON marketplace_data_revenue
    FOR ALL USING (
        product_id IN (SELECT id FROM marketplace_data_products WHERE creator_tenant_id = current_setting('app.current_tenant_id')::uuid) OR
        subscription_id IN (SELECT id FROM marketplace_data_subscriptions WHERE subscriber_tenant_id = current_setting('app.current_tenant_id')::uuid)
    );

CREATE POLICY marketplace_premium_matching_access ON marketplace_premium_matching
    FOR ALL USING (requester_tenant_id = current_setting('app.current_tenant_id')::uuid);

COMMIT;

-- =====================================================
-- MIGRATION COMPLETION STATUS
-- =====================================================
SELECT 'Migration 008: Data Products Marketplace Schema completed successfully' as status;
